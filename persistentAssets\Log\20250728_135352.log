with:1185  hight:378
[logic/login/CLoginIcnTiShi.lua:2]:!!!!!!!!!!!!!!!!!CLoginIcnTiShi!!!!!!!!!!!!!!!!!
谷歌支付初始化失败
谷歌支付初始化失败
[logic/misc/CShareCtrl.lua:42]:jit    true    SSE3    SSE4.1    BMI2    fold    cse    dce    fwd    dse    narrow    loop    abc    sink    fuse
[logic/login/CLoginCtrl.lua:5]:公告初始化...
[logic/base/CHttpCtrl.lua:32]:http get ->    https://openapi.baidu.com/oauth/2.0/token?client_id=rlU84dprH6I7sMjBkqM0gizl&grant_type=client_credentials&client_secret=c15395c2433c4a904d22aa27eab8321b&
[logic/ui/CViewCtrl.lua:94]:CNotifyView ShowView
[logic/ui/CViewCtrl.lua:94]:CBottomView ShowView
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x6eb85c48"
|  json_result = true
|  timer = 16
}
[logic/audio/CSpeechCtrl.lua:72]:OnTokenGet err
[logic/base/CResCtrl.lua:199]:-->resource init done!!! 12
[logic/ui/CViewBase.lua:125]:CNotifyView LoadDone!
[logic/ui/CViewBase.lua:125]:CBottomView LoadDone!
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {}
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/login/CLoginIcnTiShi.lua:4]:~~~~~~~~~~~~CLoginIcnTiShi.ctor~~~~~~~~~~~~~~~~~~
[logic/base/CHttpCtrl.lua:32]:http get ->    http://************:88/Note/note2408.json
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x799a1df8"
|  json_result = true
|  timer = 61
}
[core/table.lua:94]:cb tResult-> = {
|  info = {
|  |  role_list = {}
|  |  server_info = {
|  |  |  groups = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 1
|  |  |  |  |  name = "1-10区"
|  |  |  |  }
|  |  |  }
|  |  |  infoList = {
|  |  |  |  [1] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]服务器在8月16日22点00分进行不停机维护，解决ios无法使用可选御灵道具，0点掉线问题！[-]"
|  |  |  |  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "维护公告"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]欢迎来到《剑与火之歌（0.05折）》！[-]"
|  |  |  |  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "开服了"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]单日累充活动[-]"
|  |  |  |  |  |  |  |  title = "【活动类型】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [10] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*120[-]"
|  |  |  |  |  |  |  |  title = "单日充值1500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [11] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1[-]"
|  |  |  |  |  |  |  |  title = "单日充值2500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [12] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*5、万能碎片*500、原石盒子*20、附魔材料盒子*20[-]"
|  |  |  |  |  |  |  |  title = "单日充值5000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [13] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.每日均可参与，高档位可领所有低档位；
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "【活动时间】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]单日真实充值达到指定金额，即可领取该档位的奖励，本活动需要对应真实折后充值参与（高档位可以领取低档位的奖励)，每日活动重置[-]"
|  |  |  |  |  |  |  |  title = "【活动说明】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  |  |  |  title = "【领取方式】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "[ff0000]武器原石*5、星梦矿*10、金币*20000[-]"
|  |  |  |  |  |  |  |  title = "单日充值50元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*10、体力药水*1、鲜肉包*30、金币*100000[-]"
|  |  |  |  |  |  |  |  title = "单日充值150元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [7] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*20、体力药水*2、焦糖包*60、金币*200000[-]"
|  |  |  |  |  |  |  |  title = "单日充值250元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [8] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1[-]"
|  |  |  |  |  |  |  |  title = "单日充值500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [9] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3[-]"
|  |  |  |  |  |  |  |  title = "单日充值1000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "单日累充"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久累计充值活动[-]"
|  |  |  |  |  |  |  |  title = "活动名称："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [10] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*400、一发入魂碎片*100、原石盒子*40、附魔材料盒子*40、金币*3000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值7000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [11] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*700、一发入魂碎片*200、原石盒子*50、附魔材料盒子*50、金币*5000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值10000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [12] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.各档位奖励永久仅可获得一次返利，高档位可领所有低档位。
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额，活动解释权归游戏方所有。[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  |  |  |  title = "领取方式："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]活动为永久累计真实充值，高档位可领取低档位所有物品（每个档位奖励仅可领取一次）[-]"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "[ff0000]梦觉书·零*50、星梦矿*50、金币*10000[-]"
|  |  |  |  |  |  |  |  title = "累计充值300元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "[ff0000]2星精英伙伴*10、原石盒子*5、附魔材料盒子*5、金币*50000[-]"
|  |  |  |  |  |  |  |  title = "累计充值500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "[ff0000]2星精英伙伴*20、原石盒子*10、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  |  |  |  title = "累计充值1000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [7] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*1、原石盒子*30、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  |  |  |  title = "累计充值2000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [8] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*100、一发入魂碎片*100、原石盒子*20、附魔材料盒子*20、金币*1000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值3000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [9] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*200、一发入魂碎片*200、原石盒子*30、附魔材料盒子*30、金币*2000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值5000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "累计充值"
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]所有区服开服之日起3天内，第一个累计实际真实充值金额1000元的玩家，可获得该区服务器冠名权+3星传说伙伴任选*1（需提供具体道具名，伙伴包括：亦候、袁雀、白殊、莹月、奉主夜鹤、浮屠僧）[-]"
|  |  |  |  |  |  |  |  title = "服务器冠名活动："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.达成条件的玩家需向客服申请，核实后将于5个工作日内完成冠名及发放奖励至玩家邮箱；
2.资格获得者可以自定义自己所在区服的服务器名称，冠名后不可修改，请慎重取名；
3.更改后，该区服名称永久有效；
4.服务器命名规则：不超过4个汉字，不支持符号、英文，禁止色情反动、诋毁他人等不文明或破坏社会风气的词语。
后面满足条件的玩家无法获得冠名权限但新服前三名满足条件的玩家都可以联系客服领取到冠名奖励呦！

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "冠名活动"
|  |  |  |  }
|  |  |  }
|  |  |  ports = {}
|  |  |  role_list = ""
|  |  |  servers = {
|  |  |  |  [1] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 1
|  |  |  |  |  ip = "************"
|  |  |  |  |  name = "剑与火之歌"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1711501140
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10001"
|  |  |  |  |  start_time = 1711501140
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  }
|  |  }
|  |  token = ""
|  }
}
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 1
|  |  |  |  ip = "************"
|  |  |  |  name = "剑与火之歌"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1711501140
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 1
|  |  |  |  start_time = 1711501140
|  |  |  |  state = 1
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:设置公告: = {
|  [1] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]服务器在8月16日22点00分进行不停机维护，解决ios无法使用可选御灵道具，0点掉线问题！[-]"
|  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "维护公告"
|  }
|  [2] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]欢迎来到《剑与火之歌（0.05折）》！[-]"
|  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "开服了"
|  }
|  [3] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]单日累充活动[-]"
|  |  |  |  |  title = "【活动类型】"
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*120[-]"
|  |  |  |  |  title = "单日充值1500元："
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1[-]"
|  |  |  |  |  title = "单日充值2500元："
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*5、万能碎片*500、原石盒子*20、附魔材料盒子*20[-]"
|  |  |  |  |  title = "单日充值5000元："
|  |  |  |  }
|  |  |  |  [13] = {
|  |  |  |  |  text = "[ff0000]1.每日均可参与，高档位可领所有低档位；
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "【活动时间】"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]单日真实充值达到指定金额，即可领取该档位的奖励，本活动需要对应真实折后充值参与（高档位可以领取低档位的奖励)，每日活动重置[-]"
|  |  |  |  |  title = "【活动说明】"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  title = "【领取方式】"
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "[ff0000]武器原石*5、星梦矿*10、金币*20000[-]"
|  |  |  |  |  title = "单日充值50元："
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*10、体力药水*1、鲜肉包*30、金币*100000[-]"
|  |  |  |  |  title = "单日充值150元："
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*20、体力药水*2、焦糖包*60、金币*200000[-]"
|  |  |  |  |  title = "单日充值250元："
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  text = "[ff0000]装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1[-]"
|  |  |  |  |  title = "单日充值500元："
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  text = "[ff0000]装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3[-]"
|  |  |  |  |  title = "单日充值1000元："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "单日累充"
|  }
|  [4] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]永久累计充值活动[-]"
|  |  |  |  |  title = "活动名称："
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  text = "[ff0000]万能碎片*400、一发入魂碎片*100、原石盒子*40、附魔材料盒子*40、金币*3000000[-]"
|  |  |  |  |  title = "累计充值7000元："
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  text = "[ff0000]万能碎片*700、一发入魂碎片*200、原石盒子*50、附魔材料盒子*50、金币*5000000[-]"
|  |  |  |  |  title = "累计充值10000元："
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  text = "[ff0000]1.各档位奖励永久仅可获得一次返利，高档位可领所有低档位。
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额，活动解释权归游戏方所有。[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  title = "领取方式："
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]活动为永久累计真实充值，高档位可领取低档位所有物品（每个档位奖励仅可领取一次）[-]"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "[ff0000]梦觉书·零*50、星梦矿*50、金币*10000[-]"
|  |  |  |  |  title = "累计充值300元："
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "[ff0000]2星精英伙伴*10、原石盒子*5、附魔材料盒子*5、金币*50000[-]"
|  |  |  |  |  title = "累计充值500元："
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "[ff0000]2星精英伙伴*20、原石盒子*10、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  title = "累计充值1000元："
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*1、原石盒子*30、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  title = "累计充值2000元："
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  text = "[ff0000]万能碎片*100、一发入魂碎片*100、原石盒子*20、附魔材料盒子*20、金币*1000000[-]"
|  |  |  |  |  title = "累计充值3000元："
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  text = "[ff0000]万能碎片*200、一发入魂碎片*200、原石盒子*30、附魔材料盒子*30、金币*2000000[-]"
|  |  |  |  |  title = "累计充值5000元："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "累计充值"
|  }
|  [5] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]所有区服开服之日起3天内，第一个累计实际真实充值金额1000元的玩家，可获得该区服务器冠名权+3星传说伙伴任选*1（需提供具体道具名，伙伴包括：亦候、袁雀、白殊、莹月、奉主夜鹤、浮屠僧）[-]"
|  |  |  |  |  title = "服务器冠名活动："
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]1.达成条件的玩家需向客服申请，核实后将于5个工作日内完成冠名及发放奖励至玩家邮箱；
2.资格获得者可以自定义自己所在区服的服务器名称，冠名后不可修改，请慎重取名；
3.更改后，该区服名称永久有效；
4.服务器命名规则：不超过4个汉字，不支持符号、英文，禁止色情反动、诋毁他人等不文明或破坏社会风气的词语。
后面满足条件的玩家无法获得冠名权限但新服前三名满足条件的玩家都可以联系客服领取到冠名奖励呦！

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "冠名活动"
|  }
}
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://************:8081/common/accountList"
}
[core/global.lua:59]:<color=#ffeb04>CLoginServerPage.SetServer</color>
[logic/login/CLoginCtrl.lua:154]:ConnectServer =     table:0x799AA0D0    table:0x7A5CC508
[net/CNetCtrl.lua:114]:Test连接    ************    7011
[core/table.lua:94]:-->Net Receive: login.GS2CHello = {
|  time = **********
}
[core/table.lua:94]:<--Net Send: login.C2GSQueryLogin = {
|  res_file_version = {}
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:53:56
[net/netlogin.lua:210]:netlogin.GS2CQueryLogin-->
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 1001
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginAccount = {
|  account = "w8773610226"
|  client_svn_version = 96
|  client_version = "1.15.0.0"
|  device = "82RC (LENOVO)"
|  imei = ""
|  is_qrcode = 0
|  mac = "9C-2D-CD-1C-C7-E5"
|  os = "Windows 10  (10.0.0) 64bit"
|  platform = 3
|  udid = "d1bcbaffb9feae17994f24d0a8ec9d4d08cb3f95"
}
[core/global.lua:59]:<color=#ffeb04>更新代码: 1049</color>
[logic/base/Utils.lua:402]:local framever, dllver, resver = C_api.Utils.GetResVersion()
if resver >= 96 or Utils.IsEditor() then
	function CWelfareCtrl.IsOpenCostSave(self)
		local b = false
		local t = g_TimeCtrl:GetTimeS()	
		if self.m_CostSaveStartTime ~= 0 and self.m_CostSaveEndTime ~= 0 and t > self.m_CostSaveStartTime and t < self.m_CostSaveEndTime then
			b = true
		end
		return b
	end
	function CPartnerHireView.IsCanYFRH(self, iParID)
		local d = data.partnerhiredata.DATA
		if table.index(d[iParID]["recommand_list"], "一发入魂") then
			return self:IsCanSSRDraw()
		end
	end
	function CPartnerEquipPage.ChangePart(self)
		local oItem = g_ItemCtrl:GetItem(self.m_CurItemID)
		if oItem and oItem:GetValue("level") == define.Partner.ParEquip.MaxLevel and oItem:GetValue("star") < define.Partner.ParEquip.MaxStar then
			if self.m_CurPart ~= self.m_UpStarPart and self.m_CurPart ~= self.m_UpStonePart then
				self.m_UpStarBtn:SetSelected(true)
				self:SwitchPart()
			end
		end
	end
	data.netdata.BAN["proto"]["herobox"]["item"]["GS2CItemQuickUse"] = true
end

[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 1001
}
[core/table.lua:94]:-->Net Receive: login.GS2CCheckInviteCodeResult = {
|  msg = "邀请码验证已关闭"
|  result = 1
}
[core/table.lua:94]:-->Net Receive: login.GS2CLoginAccount = {
|  account = "w8773610226"
|  role_list = {
|  |  [1] = {
|  |  |  grade = 11
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 120
|  |  |  |  weapon = 2500
|  |  |  }
|  |  |  pid = 10008
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginRole = {
|  account = "w8773610226"
|  pid = 10008
}
[core/global.lua:59]:<color=#ffeb04>读取系统设置本地数据，帐号为  w8773610226</color>
[core/global.lua:59]:<color=#ffeb04>w8773610226 的系统设置本地数据为 nil，使用默认数据</color>
[core/table.lua:94]:-->Net Receive: login.GS2CLoginRole = {
|  account = "w8773610226"
|  pid = 10008
|  role = {
|  |  active = 6
|  |  bcmd = {
|  |  |  [1] = {
|  |  |  |  idx = 208
|  |  |  }
|  |  |  [10] = {
|  |  |  |  idx = 105
|  |  |  }
|  |  |  [11] = {
|  |  |  |  idx = 106
|  |  |  }
|  |  |  [12] = {
|  |  |  |  cmd = "减速"
|  |  |  |  idx = 203
|  |  |  }
|  |  |  [13] = {
|  |  |  |  idx = 108
|  |  |  }
|  |  |  [14] = {
|  |  |  |  idx = 205
|  |  |  }
|  |  |  [15] = {
|  |  |  |  idx = 206
|  |  |  }
|  |  |  [16] = {
|  |  |  |  idx = 207
|  |  |  }
|  |  |  [2] = {
|  |  |  |  idx = 107
|  |  |  }
|  |  |  [3] = {
|  |  |  |  cmd = "禁疗"
|  |  |  |  idx = 204
|  |  |  }
|  |  |  [4] = {
|  |  |  |  cmd = "集火"
|  |  |  |  idx = 201
|  |  |  }
|  |  |  [5] = {
|  |  |  |  cmd = "封印"
|  |  |  |  idx = 202
|  |  |  }
|  |  |  [6] = {
|  |  |  |  cmd = "治疗"
|  |  |  |  idx = 101
|  |  |  }
|  |  |  [7] = {
|  |  |  |  cmd = "复活"
|  |  |  |  idx = 102
|  |  |  }
|  |  |  [8] = {
|  |  |  |  cmd = "加速"
|  |  |  |  idx = 103
|  |  |  }
|  |  |  [9] = {
|  |  |  |  cmd = "解封"
|  |  |  |  idx = 104
|  |  |  }
|  |  }
|  |  coin = 64280
|  |  energy = 123
|  |  exp = 15660
|  |  grade = 11
|  |  hp = 2846
|  |  kp_sdk_info = {
|  |  |  create_time = 1*********
|  |  |  upgrade_time = **********
|  |  }
|  |  mask = "7e6683d1ffffffe"
|  |  model_info = {
|  |  |  color = {
|  |  |  |  [1] = 0
|  |  |  }
|  |  |  shape = 120
|  |  |  weapon = 2500
|  |  }
|  |  name = "颔首之孵化者"
|  |  open_day = 61
|  |  org_fuben_cnt = 2
|  |  power = **********
|  |  school = 1
|  |  school_branch = 1
|  |  sex = 2
|  |  show_id = 10008
|  |  skill_point = 10
|  |  systemsetting = {}
|  }
|  role_token = "**********0170"
|  xg_account = "bus10008"
}
[logic/ui/CViewCtrl.lua:94]:CMainMenuView ShowView
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 0
|  active = 6
|  arenamedal = 0
|  attack = 0
|  bcmd = {
|  |  [1] = {
|  |  |  idx = 208
|  |  }
|  |  [10] = {
|  |  |  idx = 105
|  |  }
|  |  [11] = {
|  |  |  idx = 106
|  |  }
|  |  [12] = {
|  |  |  cmd = "减速"
|  |  |  idx = 203
|  |  }
|  |  [13] = {
|  |  |  idx = 108
|  |  }
|  |  [14] = {
|  |  |  idx = 205
|  |  }
|  |  [15] = {
|  |  |  idx = 206
|  |  }
|  |  [16] = {
|  |  |  idx = 207
|  |  }
|  |  [2] = {
|  |  |  idx = 107
|  |  }
|  |  [3] = {
|  |  |  cmd = "禁疗"
|  |  |  idx = 204
|  |  }
|  |  [4] = {
|  |  |  cmd = "集火"
|  |  |  idx = 201
|  |  }
|  |  [5] = {
|  |  |  cmd = "封印"
|  |  |  idx = 202
|  |  }
|  |  [6] = {
|  |  |  cmd = "治疗"
|  |  |  idx = 101
|  |  }
|  |  [7] = {
|  |  |  cmd = "复活"
|  |  |  idx = 102
|  |  }
|  |  [8] = {
|  |  |  cmd = "加速"
|  |  |  idx = 103
|  |  }
|  |  [9] = {
|  |  |  cmd = "解封"
|  |  |  idx = 104
|  |  }
|  }
|  camp = 0
|  chatself = false
|  chubeiexp = 0
|  coin = 64280
|  coin_over = 0
|  color_coin = 0
|  critical_damage = 0
|  critical_ratio = 0
|  cure_critical_ratio = 0
|  defense = 0
|  energy = 123
|  exp = 15660
|  followers = {}
|  goldcoin = 0
|  grade = 11
|  hp = 2846
|  kp_sdk_info = {
|  |  create_time = 1*********
|  |  upgrade_time = **********
|  }
|  max_hp = 0
|  medal = 0
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 120
|  |  weapon = 2500
|  }
|  name = "颔首之孵化者"
|  open_day = 61
|  org_fuben_cnt = 2
|  org_offer = 0
|  power = **********
|  res_abnormal_ratio = 0
|  res_critical_ratio = 0
|  school = 1
|  school_branch = 1
|  sex = 2
|  show_id = 10008
|  skill_point = 10
|  skin = 0
|  speed = 0
|  systemsetting = {}
|  title_info = {}
|  trapmine_point = 0
|  travel_score = 0
|  upvote_amount = 0
}
[logic/misc/CTimeCtrl.lua:64]:HeartBeat-->开启心跳包检测
[core/table.lua:94]:<--Net Send: title.C2GSTitleInfoList = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGamePushSetting = {}
[core/table.lua:94]:-->Net Receive: player.GS2CShapeList = {
|  shapes = {
|  |  [1] = 120
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CGuidanceInfo = {
|  guidanceinfo = {
|  |  key = {
|  |  |  [1] = 7000041
|  |  |  [10] = 3002099
|  |  |  [11] = 2002099
|  |  |  [12] = 7000081
|  |  |  [13] = 2006099
|  |  |  [14] = 1003099
|  |  |  [15] = 3006099
|  |  |  [16] = 3007099
|  |  |  [17] = 3008099
|  |  |  [18] = 3003099
|  |  |  [19] = 7000091
|  |  |  [2] = 7000051
|  |  |  [20] = 3004099
|  |  |  [21] = 3005099
|  |  |  [22] = 5000300
|  |  |  [23] = 1004099
|  |  |  [24] = 3009099
|  |  |  [25] = 3010099
|  |  |  [26] = 3011099
|  |  |  [27] = 1006099
|  |  |  [28] = 3016099
|  |  |  [29] = 1010099
|  |  |  [3] = 6010002
|  |  |  [4] = 1027099
|  |  |  [5] = 3001099
|  |  |  [6] = 1028099
|  |  |  [7] = 2001099
|  |  |  [8] = 7000061
|  |  |  [9] = 1001099
|  |  }
|  |  version = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>>>>>>>>>>>>>>>>>>>>> GS2CGuidanceInfo </color>
[core/table.lua:94]:-->Net Receive: travel.GS2CLoginTravelPartner = {
|  travel_partner = {
|  |  server_time = **********
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CServerGradeInfo = {
|  days = 16
|  server_grade = 95
}
[core/table.lua:94]:-->Net Receive: scene.GS2CShowScene = {
|  map_id = 101000
|  scene_id = 12
|  scene_name = "帝都"
}
[core/global.lua:59]:<color=#ffeb04>CResCtrl.MoveToSecondary</color>
[core/table.lua:94]:-->Net Receive: task.GS2CLoginTask = {
|  shimen_status = 1
|  taskdata = {
|  |  [1] = {
|  |  |  acceptnpc = 11432
|  |  |  accepttime = 1753667490
|  |  |  clientnpc = {
|  |  |  |  [1] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 418
|  |  |  |  |  }
|  |  |  |  |  name = "嘟嘟噜"
|  |  |  |  |  npcid = 27768
|  |  |  |  |  npctype = 11432
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 47500
|  |  |  |  |  |  y = 23000
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  detaildesc = "作为一名即将参加武道大会的选手，怎么可能连一只嘟噜鸟都抓不住。"
|  |  |  name = "继续抓捕"
|  |  |  patrolinfo = {}
|  |  |  pickiteminfo = {}
|  |  |  placeinfo = {}
|  |  |  shapeinfo = {}
|  |  |  statusinfo = {
|  |  |  |  note = "Accept Task"
|  |  |  |  status = 1
|  |  |  }
|  |  |  submitnpc = 5004
|  |  |  target = 5004
|  |  |  targetdesc = "跟上嘟嘟噜"
|  |  |  taskid = 10379
|  |  |  taskitem = {}
|  |  |  tasktype = 9
|  |  |  traceinfo = {
|  |  |  |  cur_mapid = 101000
|  |  |  |  cur_posx = 47200
|  |  |  |  cur_posy = 23300
|  |  |  |  mapid = 101000
|  |  |  |  npctype = 11432
|  |  |  |  pos_x = 44700
|  |  |  |  pos_y = 26500
|  |  |  }
|  |  |  type = 2
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>login task</color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4ec58418"
|  |  AssociatedPick = "function: 0x4ec58478"
|  |  AssociatedSubmit = "function: 0x4ec58448"
|  |  CreateDefalutData = "function: 0x4ec563f8"
|  |  GetChaptetFubenData = "function: 0x4ec58138"
|  |  GetProgressThing = "function: 0x4ec584d8"
|  |  GetRemainTime = "function: 0x4ec56390"
|  |  GetStatus = "function: 0x4ec58538"
|  |  GetTaskClientExtStrDic = "function: 0x4ec584a8"
|  |  GetTaskTypeSpriteteName = "function: 0x4ec58108"
|  |  GetTraceInfo = "function: 0x4ec58260"
|  |  GetTraceNpcType = "function: 0x4ec580d8"
|  |  GetValue = "function: 0x4ec581a0"
|  |  IsAbandon = "function: 0x4ec58200"
|  |  IsAddEscortDynamicNpc = "function: 0x4ec58870"
|  |  IsMissMengTask = "function: 0x4ec563c0"
|  |  IsPassChaterFuben = "function: 0x4ec58168"
|  |  IsTaskSpecityAction = "function: 0x4ec58230"
|  |  IsTaskSpecityCategory = "function: 0x4ec583e8"
|  |  New = "function: 0x4ec5cd68"
|  |  NewByData = "function: 0x4ec55938"
|  |  RaiseProgressIdx = "function: 0x4ec58508"
|  |  RefreshTask = "function: 0x4ec581d0"
|  |  ResetEndTime = "function: 0x4ec56460"
|  |  SetStatus = "function: 0x4ec588a0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4ec56360"
|  }
|  m_CData = {
|  |  ChapterFb = "1,6"
|  |  autoDoNextTask = 10380
|  |  clientExtStr = ""
|  |  name = "继续抓捕"
|  |  submitNpcId = 5004
|  |  submitRewardStr = {
|  |  |  [1] = "R1379"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 11432
|  |  accepttime = 1753667490
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 418
|  |  |  |  }
|  |  |  |  name = "嘟嘟噜"
|  |  |  |  npcid = 27768
|  |  |  |  npctype = 11432
|  |  |  |  pos_info = {
|  |  |  |  |  x = 47500
|  |  |  |  |  y = 23000
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "作为一名即将参加武道大会的选手，怎么可能连一只嘟噜鸟都抓不住。"
|  |  isdone = 0
|  |  name = "继续抓捕"
|  |  patrolinfo = {}
|  |  pickiteminfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "Accept Task"
|  |  |  status = 1
|  |  }
|  |  submitnpc = 5004
|  |  target = 5004
|  |  targetdesc = "跟上嘟嘟噜"
|  |  taskid = 10379
|  |  taskitem = {}
|  |  tasktype = 9
|  |  time = 0
|  |  traceinfo = {
|  |  |  cur_mapid = 101000
|  |  |  cur_posx = 47200
|  |  |  cur_posy = 23300
|  |  |  mapid = 101000
|  |  |  npctype = 11432
|  |  |  pos_x = 44700
|  |  |  pos_y = 26500
|  |  }
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CTeachProgress = {}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshPartnerTask = {
|  partnertask_progress = {
|  |  [1] = {
|  |  |  parid = 302
|  |  |  status = 1
|  |  |  taskid = 62080
|  |  }
|  |  [2] = {
|  |  |  parid = 502
|  |  |  status = 1
|  |  |  taskid = 62090
|  |  }
|  |  [3] = {
|  |  |  parid = 403
|  |  |  status = 1
|  |  |  taskid = 62169
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1016
|  |  [2] = 3005
|  }
}
[core/table.lua:94]:-->Net Receive: state.GS2CLoginState = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginHuodongInfo = {
|  convoyinfo = {
|  |  convoy_partner = 1201
|  |  free_time = 1
|  |  pool_info = {
|  |  |  [1] = {
|  |  |  |  level = 1
|  |  |  |  partnerid = 1504
|  |  |  |  pos = 1
|  |  |  |  rewardid = 1001
|  |  |  }
|  |  |  [2] = {
|  |  |  |  level = 2
|  |  |  |  partnerid = 1201
|  |  |  |  pos = 2
|  |  |  |  rewardid = 1002
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [3] = {
|  |  |  |  level = 3
|  |  |  |  partnerid = 1009
|  |  |  |  pos = 3
|  |  |  |  rewardid = 1003
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [4] = {
|  |  |  |  level = 4
|  |  |  |  partnerid = 1510
|  |  |  |  pos = 4
|  |  |  |  rewardid = 1004
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [5] = {
|  |  |  |  level = 5
|  |  |  |  partnerid = 1020
|  |  |  |  pos = 5
|  |  |  |  rewardid = 1005
|  |  |  |  weight = 25
|  |  |  }
|  |  }
|  |  selected_pos = 2
|  |  target_npc = 5015
|  }
|  dailytrain = {}
|  hireinfo = {
|  |  [1] = {
|  |  |  parid = 502
|  |  |  times = 1
|  |  }
|  |  [2] = {
|  |  |  parid = 403
|  |  |  times = 1
|  |  }
|  }
|  huntinfo = {}
}
[core/table.lua:94]:-->Net Receive: item.GS2CLoginItem = {
|  itemdata = {
|  |  [1] = {
|  |  |  amount = 3
|  |  |  create_time = 1752740705
|  |  |  id = 1
|  |  |  itemlevel = 2
|  |  |  name = "一星云母"
|  |  |  sid = 14031
|  |  }
|  |  [10] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 121
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 24
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 2
|  |  |  |  stone_sid = 4000000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 12
|  |  |  itemlevel = 1
|  |  |  name = "练习飘逸巾"
|  |  |  power = 24
|  |  |  sid = 2200000
|  |  }
|  |  [11] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 3
|  |  |  |  stone_sid = 4110000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 10
|  |  |  itemlevel = 1
|  |  |  name = "练习絮语衣"
|  |  |  power = 32
|  |  |  sid = 2320000
|  |  }
|  |  [12] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 4
|  |  |  |  stone_sid = 4200000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 13
|  |  |  itemlevel = 1
|  |  |  name = "练习草织戒"
|  |  |  power = 19
|  |  |  sid = 2400000
|  |  }
|  |  [13] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 364
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 72
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 5
|  |  |  |  stone_sid = 4310000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 11
|  |  |  itemlevel = 1
|  |  |  name = "练习离叶腰"
|  |  |  power = 72
|  |  |  sid = 2520000
|  |  }
|  |  [14] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "speed"
|  |  |  |  |  value = 33
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 11
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 77
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 6
|  |  |  |  stone_sid = 4410000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 14
|  |  |  itemlevel = 1
|  |  |  name = "练习青藤鞋"
|  |  |  power = 77
|  |  |  sid = 2620000
|  |  }
|  |  [2] = {
|  |  |  amount = 5
|  |  |  create_time = 1752738555
|  |  |  id = 2
|  |  |  itemlevel = 5
|  |  |  name = "扫荡券"
|  |  |  sid = 10030
|  |  }
|  |  [3] = {
|  |  |  amount = 1
|  |  |  create_time = 1752734064
|  |  |  id = 3
|  |  |  itemlevel = 2
|  |  |  name = "3级符石礼包"
|  |  |  sid = 14103
|  |  }
|  |  [4] = {
|  |  |  amount = 2
|  |  |  create_time = 1752740705
|  |  |  id = 4
|  |  |  itemlevel = 2
|  |  |  name = "深蓝琥珀"
|  |  |  sid = 14021
|  |  }
|  |  [5] = {
|  |  |  amount = 5
|  |  |  create_time = 1753667289
|  |  |  id = 5
|  |  |  itemlevel = 4
|  |  |  name = "万能碎片"
|  |  |  sid = 14002
|  |  }
|  |  [6] = {
|  |  |  amount = 1
|  |  |  create_time = 1752738555
|  |  |  id = 6
|  |  |  itemlevel = 2
|  |  |  name = "3级绯红宝石"
|  |  |  sid = 18002
|  |  }
|  |  [7] = {
|  |  |  amount = 1
|  |  |  create_time = 1751883929
|  |  |  id = 7
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 1
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101005
|  |  }
|  |  [8] = {
|  |  |  amount = 1
|  |  |  create_time = 1752134839
|  |  |  id = 8
|  |  |  itemlevel = 3
|  |  |  name = "梦觉书·低"
|  |  |  sid = 27402
|  |  }
|  |  [9] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 1
|  |  |  |  stone_sid = 3100000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 9
|  |  |  itemlevel = 1
|  |  |  name = "练习红魔钺"
|  |  |  power = 57
|  |  |  sid = 2100000
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>loginitem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginChapterInfo = {
|  extrareward_info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 6
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = 1
|  |  |  level = 4
|  |  |  type = 1
|  |  }
|  }
|  finalchapter = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 7
|  |  |  open = 1
|  |  |  type = 1
|  |  }
|  }
|  totalstar_info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  star = 18
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: handbook.GS2CLoginBookList = {
|  book_list = {
|  |  [1] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315021
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115021
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100502
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313021
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  id = 313022
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113021
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100302
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [3] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 314031
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114031
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100403
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  }
|  red_points = {
|  |  [1] = {
|  |  |  book_type = 1
|  |  |  red_point = 1
|  |  }
|  |  [2] = {
|  |  |  book_type = 2
|  |  }
|  |  [3] = {
|  |  |  book_type = 3
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: team.GS2CInviteInfo = {}
[core/table.lua:94]:-->Net Receive: mail.GS2CLoginMail = {
|  simpleinfo = {
|  |  [1] = {
|  |  |  createtime = 1752549158
|  |  |  hasattach = 1
|  |  |  keeptime = 7776000
|  |  |  mailid = 1
|  |  |  subject = "喵小萌的来信"
|  |  |  title = "冲榜返利"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: friend.GS2CLoginFriend = {}
[core/global.lua:59]:<color=#ffeb04>好友版本一致，无需更新</color>
[core/table.lua:94]:-->Net Receive: friend.GS2CApplyList = {}
[core/table.lua:94]:-->Net Receive: friend.GS2CFriendSetting = {
|  setting = {
|  |  notify = 1
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CDrawCardUI = {}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartnerList = {
|  partner_list = {
|  |  [1] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 461
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 93
|  |  |  equip_list = {
|  |  |  |  [1] = 7
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 10470
|  |  |  grade = 10
|  |  |  hp = 3120
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 3120
|  |  |  model_info = {
|  |  |  |  shape = 302
|  |  |  |  skin = 203020
|  |  |  }
|  |  |  name = "重华"
|  |  |  parid = 1
|  |  |  partner_type = 302
|  |  |  patahp = 3120
|  |  |  power = 1104
|  |  |  power_rank = 2
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30203
|  |  |  |  }
|  |  |  }
|  |  |  speed = 420
|  |  |  star = 1
|  |  }
|  |  [2] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 349
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 79
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 5650
|  |  |  grade = 7
|  |  |  hp = 2611
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 2611
|  |  |  model_info = {
|  |  |  |  shape = 502
|  |  |  |  skin = 205020
|  |  |  }
|  |  |  name = "马面面"
|  |  |  parid = 2
|  |  |  partner_type = 502
|  |  |  patahp = 2611
|  |  |  power = 882
|  |  |  power_rank = 2
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 500
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50203
|  |  |  |  }
|  |  |  }
|  |  |  speed = 315
|  |  |  star = 1
|  |  }
|  |  [3] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 316
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 600
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 70
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 3070
|  |  |  grade = 4
|  |  |  hp = 2441
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 2441
|  |  |  model_info = {
|  |  |  |  shape = 403
|  |  |  |  skin = 204030
|  |  |  }
|  |  |  name = "蛇姬"
|  |  |  parid = 3
|  |  |  partner_type = 403
|  |  |  patahp = 2441
|  |  |  power = 798
|  |  |  power_rank = 2
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40301
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40302
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40303
|  |  |  |  }
|  |  |  }
|  |  |  speed = 735
|  |  |  star = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartner = {
|  fight_info = {
|  |  [1] = {
|  |  |  parid = 1
|  |  |  pos = 1
|  |  }
|  |  [2] = {
|  |  |  parid = 2
|  |  |  pos = 2
|  |  }
|  |  [3] = {
|  |  |  parid = 3
|  |  |  pos = 3
|  |  }
|  }
|  owned_equip_list = {
|  |  [1] = 6101001
|  }
|  owned_partner_list = {
|  |  [1] = 302
|  |  [2] = 502
|  |  [3] = 403
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPicturePosList = {}
[core/table.lua:94]:-->Net Receive: skill.GS2CLoginSkill = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3001
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3002
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  needcost = 15
|  |  |  sk = 3004
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  needcost = 25
|  |  |  sk = 3005
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3006
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3007
|  |  |  type = 1
|  |  }
|  }
}
[logic/skill/CSkillCtrl.lua:112]:技能数据登陆协议返回:
[core/table.lua:94]:Table = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3001
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3002
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  needcost = 15
|  |  |  sk = 3004
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  needcost = 25
|  |  |  sk = 3005
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3006
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3007
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginParSoulPlan = {}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterScene = {
|  eid = 32
|  pos_info = {
|  |  face_y = 90675
|  |  x = 46719
|  |  y = 25599
|  }
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04>result  nil</color>
[core/global.lua:59]:<color=#ffeb04>=00000=  userdata: 0x7f2ae5f0 nil</color>
[logic/ui/CSprite.lua:159]:DynamicSprite dSprInfo    Item    10012
[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:217: in function <[string "logic/base/CResCtrl"]:212>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:217: in function <[string "logic/base/CResCtrl"]:212>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/global.lua:59]:<color=#ffeb04>读取地图： 1020</color>
[core/global.lua:59]:<color=#ffeb04>Unity2017.4.30f1 localScale</color>
[logic/ui/CViewCtrl.lua:104]:CLoginView     CloseView
[logic/base/CResCtrl.lua:599]:res gc step start!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/global.lua:59]:<color=#ffeb04>地图加载完成: 1020 ,当前地图: nil</color>
[core/table.lua:94]:Table = {
|  cur_mapid = 101000
|  cur_posx = 47200
|  cur_posy = 23300
|  mapid = 101000
|  npctype = 11432
|  pos_x = 44700
|  pos_y = 26500
}
[core/table.lua:94]:Table = {}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcFightInfoList = {
|  info_list = {
|  |  [1] = {
|  |  |  npc_type = 10024
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [10] = {
|  |  |  npc_type = 10007
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [11] = {
|  |  |  npc_type = 10008
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [12] = {
|  |  |  npc_type = 10009
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [13] = {
|  |  |  npc_type = 10011
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [14] = {
|  |  |  npc_type = 10012
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [15] = {
|  |  |  npc_type = 10013
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [16] = {
|  |  |  npc_type = 10000
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [17] = {
|  |  |  npc_type = 10023
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [18] = {
|  |  |  npc_type = 10014
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [19] = {
|  |  |  npc_type = 10015
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [2] = {
|  |  |  npc_type = 10002
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [20] = {
|  |  |  npc_type = 10016
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [21] = {
|  |  |  npc_type = 10017
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [22] = {
|  |  |  npc_type = 10018
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [23] = {
|  |  |  npc_type = 10019
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [24] = {
|  |  |  npc_type = 10020
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [25] = {
|  |  |  npc_type = 10021
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [26] = {
|  |  |  npc_type = 10022
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [3] = {
|  |  |  npc_type = 10010
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [4] = {
|  |  |  npc_type = 10001
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [5] = {
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [6] = {
|  |  |  npc_type = 10003
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [7] = {
|  |  |  npc_type = 10004
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [8] = {
|  |  |  npc_type = 10005
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [9] = {
|  |  |  npc_type = 10006
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CRefreshRewardBack = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 36364
|  |  |  type = "zsk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 36364
|  |  |  type = "yk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
|  mask = "c"
}
[core/table.lua:94]:CNetCtrl解析mask: GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 36364
|  |  |  type = "zsk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 36364
|  |  |  type = "yk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: store.GS2CPayForColorCoinInfo = {
|  colorcoin_list = {
|  |  [1] = {
|  |  |  key = "goldcoinstore_1012"
|  |  }
|  |  [10] = {
|  |  |  key = "goldcoinstore_2011"
|  |  }
|  |  [11] = {
|  |  |  key = "goldcoinstore_2012"
|  |  }
|  |  [12] = {
|  |  |  key = "goldcoinstore_2013"
|  |  }
|  |  [13] = {
|  |  |  key = "goldcoinstore_2014"
|  |  }
|  |  [14] = {
|  |  |  key = "goldcoinstore_2015"
|  |  }
|  |  [15] = {
|  |  |  key = "goldcoinstore_1015"
|  |  }
|  |  [16] = {
|  |  |  key = "goldcoinstore_1005"
|  |  }
|  |  [17] = {
|  |  |  key = "goldcoinstore_1006"
|  |  }
|  |  [18] = {
|  |  |  key = "goldcoinstore_1007"
|  |  }
|  |  [19] = {
|  |  |  key = "goldcoinstore_1008"
|  |  }
|  |  [2] = {
|  |  |  key = "goldcoinstore_1013"
|  |  }
|  |  [20] = {
|  |  |  key = "goldcoinstore_1009"
|  |  }
|  |  [21] = {
|  |  |  key = "goldcoinstore_1010"
|  |  }
|  |  [22] = {
|  |  |  key = "goldcoinstore_1011"
|  |  }
|  |  [3] = {
|  |  |  key = "goldcoinstore_1014"
|  |  }
|  |  [4] = {
|  |  |  key = "goldcoinstore_2005"
|  |  }
|  |  [5] = {
|  |  |  key = "goldcoinstore_2006"
|  |  }
|  |  [6] = {
|  |  |  key = "goldcoinstore_2007"
|  |  }
|  |  [7] = {
|  |  |  key = "goldcoinstore_2008"
|  |  }
|  |  [8] = {
|  |  |  key = "goldcoinstore_2009"
|  |  }
|  |  [9] = {
|  |  |  key = "goldcoinstore_2010"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeRewrad = {
|  end_time = 1751813939
|  start_time = 1751727600
}
[core/table.lua:94]:-->Net Receive: title.GS2CRemoveTitles = {
|  tidlist = {
|  |  [1] = 1001
|  |  [2] = 1002
|  |  [3] = 1003
|  |  [4] = 1004
|  |  [5] = 1005
|  |  [6] = 1006
|  |  [7] = 1007
|  |  [8] = 1008
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "8段"
|  |  tid = 1008
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "2段"
|  |  tid = 1002
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "3段"
|  |  tid = 1003
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "4段"
|  |  tid = 1004
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "5段"
|  |  tid = 1005
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "6段"
|  |  tid = 1006
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "7段"
|  |  tid = 1007
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginTrapmine = {
|  rare_monster = {
|  |  [1] = {
|  |  |  map_id = 202000
|  |  |  partypes = {
|  |  |  |  [1] = 312
|  |  |  |  [2] = 313
|  |  |  |  [3] = 316
|  |  |  }
|  |  }
|  |  [2] = {
|  |  |  map_id = 204000
|  |  |  partypes = {
|  |  |  |  [1] = 404
|  |  |  |  [2] = 402
|  |  |  |  [3] = 407
|  |  |  }
|  |  }
|  |  [3] = {
|  |  |  map_id = 205000
|  |  |  partypes = {
|  |  |  |  [1] = 508
|  |  |  |  [2] = 513
|  |  |  |  [3] = 509
|  |  |  }
|  |  }
|  |  [4] = {
|  |  |  map_id = 206000
|  |  |  partypes = {
|  |  |  |  [1] = 410
|  |  |  |  [2] = 414
|  |  |  |  [3] = 415
|  |  |  }
|  |  }
|  |  [5] = {
|  |  |  map_id = 210400
|  |  |  partypes = {
|  |  |  |  [1] = 504
|  |  |  |  [2] = 506
|  |  |  |  [3] = 507
|  |  |  }
|  |  }
|  |  [6] = {
|  |  |  map_id = 200000
|  |  |  partypes = {
|  |  |  |  [1] = 416
|  |  |  |  [2] = 417
|  |  |  |  [3] = 502
|  |  |  }
|  |  }
|  |  [7] = {
|  |  |  map_id = 201000
|  |  |  partypes = {
|  |  |  |  [1] = 308
|  |  |  |  [2] = 301
|  |  |  |  [3] = 302
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliReddot = {
|  cday = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CNewFieldBoss = {
|  bossid = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraWarState = {
|  time = 1753871152
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraQueueStatus = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CDailySignInfo = {
|  sign_info = {
|  |  [1] = {
|  |  |  key = "week"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COnlineGift = {
|  onlinetime = 297
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHeroBoxRecord = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeScore = {
|  cur_id = 1
|  end_time = 1752505139
|  score_info = {}
|  start_time = 1751209200
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayBuy = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CHistoryCharge = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFirstChargeUI = {
|  bOpen = true
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliPoint = {
|  point = 4
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawCnt = {
|  cnt = 7
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliTime = {
|  endtime = 1752505139
|  starttime = 1751209200
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  mask = "1997c2e0000000"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  give_org_equip = {}
|  give_org_wish = {}
|  is_equip_wish = 0
|  is_org_wish = 0
|  org_build_status = 0
|  org_build_time = 0
|  org_id = 0
|  org_leader = ""
|  org_level = 0
|  org_pos = 0
|  org_red_packet = 0
|  org_sign_reward = 0
|  org_status = 0
|  orgname = ""
}
[core/table.lua:94]:-->Net Receive: player.GS2CTodayInfo = {
|  info = {
|  |  mask = "0"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: todyInfo = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGameShare = {
|  game_share = {
|  |  [1] = {
|  |  |  type = "draw_card_share"
|  |  }
|  |  [2] = {
|  |  |  type = "skin_share"
|  |  }
|  |  [3] = {
|  |  |  type = "picture_share"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardInfo = {
|  breed_val = 6
|  login_day = 7
|  rewarded_day = 104
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  abnormal_attr_ratio = 550
|  |  attack = 315
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  mask = "87ff00"
|  |  max_hp = 2928
|  |  power = 921
|  |  res_abnormal_ratio = 550
|  |  res_critical_ratio = 500
|  |  speed = 753
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 550
|  attack = 315
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  hp = 0
|  max_hp = 2928
|  power = 921
|  res_abnormal_ratio = 550
|  res_critical_ratio = 500
|  speed = 753
}
[core/table.lua:94]:-->Net Receive: house.GS2CEnterHouse = {
|  buff_info = {}
|  furniture_info = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  lock_status = 1
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  type = 2
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  type = 3
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  type = 4
|  |  }
|  |  [5] = {
|  |  |  level = 1
|  |  |  type = 5
|  |  }
|  }
|  handle_type = 1
|  max_train = 1
|  max_warm_degree = 1000
|  owner_pid = 10008
|  partner_info = {
|  |  [1] = {
|  |  |  coin = 2871
|  |  |  type = 1001
|  |  }
|  |  [2] = {
|  |  |  type = 1003
|  |  }
|  }
|  warm_degree = 156
}
[core/table.lua:94]:-->Net Receive: house.GS2CPartnerExchangeUI = {
|  handle_type = 1
|  love_cnt = 10
|  max_gift_cnt = 10
|  max_love_cnt = 10
|  partner_gift_cnt = 10
}
[core/table.lua:94]:-->Net Receive: house.GS2COpenWorkDesk = {
|  desk_info = {
|  |  [1] = {
|  |  |  lock_status = 1
|  |  |  pos = 1
|  |  |  status = 1
|  |  }
|  |  [2] = {
|  |  |  pos = 2
|  |  |  status = 1
|  |  }
|  |  [3] = {
|  |  |  pos = 3
|  |  |  status = 1
|  |  }
|  |  [4] = {
|  |  |  lock_status = 1
|  |  |  pos = 4
|  |  |  status = 1
|  |  }
|  }
|  handle_type = 1
|  owner_pid = 10008
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 1
|  |  |  }
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  blist = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  id = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayMain = {
|  server_day = 21
}
[core/table.lua:94]:-->Net Receive: task.GS2CLoginAchieveTask = {
|  info = {
|  |  [1] = {
|  |  |  achievetype = 1
|  |  |  degree = 1
|  |  |  describe = "领取1次在线奖励"
|  |  |  name = "在线奖励"
|  |  |  target = 1
|  |  |  taskid = 31001
|  |  }
|  |  [2] = {
|  |  |  achievetype = 2
|  |  |  degree = 1
|  |  |  describe = "穿戴两件符文"
|  |  |  name = "穿戴符文（2）"
|  |  |  target = 2
|  |  |  taskid = 31533
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>LoginAchieveTask</color>
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1102
|  |  |  }
|  |  |  name = "峰馆长"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 2
|  |  npctype = 5040
|  }
|  eid = 1
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 1
|  pos_info = {
|  |  x = 31800
|  |  y = 17600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1102
|  }
|  name = "峰馆长"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1016
|  |  |  }
|  |  |  name = "水生"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 196
|  |  npctype = 5009
|  }
|  eid = 17
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 17
|  pos_info = {
|  |  x = 24000
|  |  y = 22000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1016
|  }
|  name = "水生"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1010
|  |  |  }
|  |  |  name = "丽丝"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 69
|  |  npctype = 5004
|  }
|  eid = 12
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 12
|  pos_info = {
|  |  x = 45600
|  |  y = 26900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1010
|  }
|  name = "丽丝"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1011
|  |  |  }
|  |  |  name = "遥"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 72
|  |  npctype = 5005
|  }
|  eid = 13
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 13
|  pos_info = {
|  |  x = 45500
|  |  y = 20000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1011
|  }
|  name = "遥"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 417
|  |  |  }
|  |  |  name = "松姑子"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 220
|  |  npctype = 5019
|  }
|  eid = 21
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 21
|  pos_info = {
|  |  x = 42300
|  |  y = 24600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 417
|  }
|  name = "松姑子"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1012
|  |  |  }
|  |  |  name = "克瑞斯汀"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 24
|  |  npctype = 5048
|  }
|  eid = 7
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 7
|  pos_info = {
|  |  x = 20200
|  |  y = 21100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1012
|  }
|  name = "克瑞斯汀"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: title.GS2CTitleInfoList = {
|  infos = {
|  |  [1] = {
|  |  |  name = "8段"
|  |  |  tid = 1008
|  |  }
|  |  [10] = {
|  |  |  name = "应用宝贵族"
|  |  |  tid = 1017
|  |  }
|  |  [11] = {
|  |  |  name = "王者公会"
|  |  |  tid = 1018
|  |  }
|  |  [12] = {
|  |  |  name = "王者公会会长"
|  |  |  tid = 1019
|  |  }
|  |  [13] = {
|  |  |  name = "黄金公会"
|  |  |  tid = 1020
|  |  }
|  |  [14] = {
|  |  |  name = "黄金公会会长"
|  |  |  tid = 1021
|  |  }
|  |  [15] = {
|  |  |  name = "白银公会"
|  |  |  tid = 1022
|  |  }
|  |  [16] = {
|  |  |  name = "白银公会会长"
|  |  |  tid = 1023
|  |  }
|  |  [17] = {
|  |  |  name = "明日之星"
|  |  |  tid = 1024
|  |  }
|  |  [18] = {
|  |  |  name = "独孤求败"
|  |  |  tid = 1025
|  |  }
|  |  [19] = {
|  |  |  name = "谁与争锋"
|  |  |  tid = 1026
|  |  }
|  |  [2] = {
|  |  |  name = "测-贵族宝"
|  |  |  tid = 1009
|  |  }
|  |  [20] = {
|  |  |  name = "所向披靡"
|  |  |  tid = 1027
|  |  }
|  |  [21] = {
|  |  |  name = "地牢终结者"
|  |  |  tid = 1028
|  |  }
|  |  [22] = {
|  |  |  name = "地牢征服者"
|  |  |  tid = 1029
|  |  }
|  |  [23] = {
|  |  |  name = "地牢破坏者"
|  |  |  tid = 1030
|  |  }
|  |  [24] = {
|  |  |  name = "眯眯眼是变态"
|  |  |  tid = 1031
|  |  }
|  |  [25] = {
|  |  |  name = "邻家妹妹"
|  |  |  tid = 1032
|  |  }
|  |  [26] = {
|  |  |  name = "兄控"
|  |  |  tid = 1033
|  |  }
|  |  [27] = {
|  |  |  name = "农夫三拳"
|  |  |  tid = 1034
|  |  }
|  |  [28] = {
|  |  |  name = "赌神"
|  |  |  tid = 1035
|  |  }
|  |  [29] = {
|  |  |  name = "神之右手"
|  |  |  tid = 1036
|  |  }
|  |  [3] = {
|  |  |  name = "测-我就是托"
|  |  |  tid = 1010
|  |  }
|  |  [30] = {
|  |  |  name = "茶会噩梦"
|  |  |  tid = 1037
|  |  }
|  |  [31] = {
|  |  |  name = "我是傲娇"
|  |  |  tid = 1038
|  |  }
|  |  [32] = {
|  |  |  name = "闷骚美男子"
|  |  |  tid = 1039
|  |  }
|  |  [33] = {
|  |  |  name = "脸炮"
|  |  |  tid = 1040
|  |  }
|  |  [34] = {
|  |  |  name = "妹控"
|  |  |  tid = 1041
|  |  }
|  |  [35] = {
|  |  |  name = "守夜人"
|  |  |  tid = 1042
|  |  }
|  |  [36] = {
|  |  |  name = "泡泡娘"
|  |  |  tid = 1043
|  |  }
|  |  [37] = {
|  |  |  name = "姐控"
|  |  |  tid = 1044
|  |  }
|  |  [38] = {
|  |  |  name = "迷妹"
|  |  |  tid = 1045
|  |  }
|  |  [39] = {
|  |  |  name = "小天使"
|  |  |  tid = 1046
|  |  }
|  |  [4] = {
|  |  |  name = "测-粗壮大腿"
|  |  |  tid = 1011
|  |  }
|  |  [40] = {
|  |  |  name = "欺诈兔子"
|  |  |  tid = 1047
|  |  }
|  |  [41] = {
|  |  |  name = "天然呆"
|  |  |  tid = 1048
|  |  }
|  |  [42] = {
|  |  |  name = "开局一条狗"
|  |  |  tid = 1049
|  |  }
|  |  [43] = {
|  |  |  name = "种竹专业户"
|  |  |  tid = 1050
|  |  }
|  |  [44] = {
|  |  |  name = "顾家好男人"
|  |  |  tid = 1051
|  |  }
|  |  [45] = {
|  |  |  name = "无敌小钢炮"
|  |  |  tid = 1052
|  |  }
|  |  [46] = {
|  |  |  name = "小豆丁"
|  |  |  tid = 1053
|  |  }
|  |  [47] = {
|  |  |  name = "浪里白条"
|  |  |  tid = 1054
|  |  }
|  |  [48] = {
|  |  |  name = "话唠"
|  |  |  tid = 1055
|  |  }
|  |  [49] = {
|  |  |  name = "角落种蘑菇"
|  |  |  tid = 1056
|  |  }
|  |  [5] = {
|  |  |  name = "攻防战第一公会"
|  |  |  tid = 1012
|  |  }
|  |  [50] = {
|  |  |  name = "天然疯"
|  |  |  tid = 1057
|  |  }
|  |  [51] = {
|  |  |  name = "吃货"
|  |  |  tid = 1058
|  |  }
|  |  [52] = {
|  |  |  name = "控制狂"
|  |  |  tid = 1059
|  |  }
|  |  [53] = {
|  |  |  name = "女王大人"
|  |  |  tid = 1060
|  |  }
|  |  [54] = {
|  |  |  name = "八卦小王子"
|  |  |  tid = 1061
|  |  }
|  |  [55] = {
|  |  |  name = "三无"
|  |  |  tid = 1062
|  |  }
|  |  [56] = {
|  |  |  name = "养得累"
|  |  |  tid = 1063
|  |  }
|  |  [57] = {
|  |  |  name = "扑街之王"
|  |  |  tid = 1064
|  |  }
|  |  [58] = {
|  |  |  name = "抖S"
|  |  |  tid = 1065
|  |  }
|  |  [59] = {
|  |  |  name = "工作狂人"
|  |  |  tid = 1066
|  |  }
|  |  [6] = {
|  |  |  name = "攻防战第一司令"
|  |  |  tid = 1013
|  |  }
|  |  [60] = {
|  |  |  name = "精分中毒患者"
|  |  |  tid = 1067
|  |  }
|  |  [61] = {
|  |  |  name = "神神叨叨"
|  |  |  tid = 1068
|  |  }
|  |  [62] = {
|  |  |  name = "浮夸之神"
|  |  |  tid = 1069
|  |  }
|  |  [63] = {
|  |  |  name = "抖M"
|  |  |  tid = 1070
|  |  }
|  |  [64] = {
|  |  |  name = "自恋狂"
|  |  |  tid = 1071
|  |  }
|  |  [65] = {
|  |  |  name = "天然黑"
|  |  |  tid = 1072
|  |  }
|  |  [66] = {
|  |  |  name = "为所欲为"
|  |  |  tid = 1073
|  |  }
|  |  [67] = {
|  |  |  name = "氪金大佬"
|  |  |  tid = 1074
|  |  }
|  |  [68] = {
|  |  |  name = "应用宝帝王"
|  |  |  tid = 1075
|  |  }
|  |  [69] = {
|  |  |  name = "应用宝至尊"
|  |  |  tid = 1076
|  |  }
|  |  [7] = {
|  |  |  name = "梦魇之主"
|  |  |  tid = 1014
|  |  }
|  |  [70] = {
|  |  |  name = "公会新人"
|  |  |  tid = 1077
|  |  }
|  |  [71] = {
|  |  |  name = "公会成员"
|  |  |  tid = 1078
|  |  }
|  |  [72] = {
|  |  |  name = "公会精英"
|  |  |  tid = 1079
|  |  }
|  |  [73] = {
|  |  |  name = "公会副会"
|  |  |  tid = 1080
|  |  }
|  |  [74] = {
|  |  |  name = "公会会长"
|  |  |  tid = 1081
|  |  }
|  |  [75] = {
|  |  |  name = "情侣专属称谓"
|  |  |  tid = 1082
|  |  }
|  |  [76] = {
|  |  |  name = "命运之子"
|  |  |  tid = 1083
|  |  }
|  |  [77] = {
|  |  |  name = "不做大多数"
|  |  |  tid = 1084
|  |  }
|  |  [78] = {
|  |  |  name = "1段"
|  |  |  tid = 1001
|  |  }
|  |  [79] = {
|  |  |  name = "2段"
|  |  |  tid = 1002
|  |  }
|  |  [8] = {
|  |  |  name = "梦魇骑士"
|  |  |  tid = 1015
|  |  }
|  |  [80] = {
|  |  |  name = "3段"
|  |  |  tid = 1003
|  |  }
|  |  [81] = {
|  |  |  name = "4段"
|  |  |  tid = 1004
|  |  }
|  |  [82] = {
|  |  |  name = "5段"
|  |  |  tid = 1005
|  |  }
|  |  [83] = {
|  |  |  name = "6段"
|  |  |  tid = 1006
|  |  }
|  |  [84] = {
|  |  |  name = "7段"
|  |  |  tid = 1007
|  |  }
|  |  [9] = {
|  |  |  name = "梦魇猎人"
|  |  |  tid = 1016
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682037
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:53:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: task.C2GSSyncTraceInfo = {
|  cur_mapid = 101000
|  cur_posx = 47200
|  cur_posy = 23300
|  taskid = 10379
}
[logic/base/CResCtrl.lua:626]:res gc finish!
[logic/platform/CSdkCtrl.lua:393]:sdk upload: not init
[core/table.lua:94]:Table = {
|  [1] = "16"
|  [2] = "20"
}
[core/global.lua:59]:<color=#ffeb04>11 16 20</color>
[core/table.lua:94]:Table = {
|  [1] = "21"
|  [2] = "24"
}
[core/global.lua:59]:<color=#ffeb04>11 21 24</color>
[core/table.lua:94]:Table = {
|  [1] = "25"
|  [2] = "27"
}
[core/global.lua:59]:<color=#ffeb04>11 25 27</color>
[logic/ui/CViewCtrl.lua:94]:CLimitRewardView ShowView
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[logic/ui/CViewBase.lua:125]:CLimitRewardView LoadDone!
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 7
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x7a5b0d40"
|  json_result = true
|  timer = 69
}
[core/table.lua:94]:<--Net Send: fuli.C2GSStartLuckDraw = {
|  type = 1
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawPos = {
|  cnt = 6
|  cost = 100
|  pos = 8
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682047
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:54:07
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: fuli.C2GSGiveLuckDraw = {}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1753682052
|  |  id = 15
|  |  itemlevel = 3
|  |  name = "吞天碎片"
|  |  sid = 20505
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#P[吞天碎片] x 1#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  sid = 20505
|  |  |  virtual = 20505
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewCtrl.lua:94]:ItemTipsSimpleInfoView ShowView
[logic/ui/CViewBase.lua:125]:ItemTipsSimpleInfoView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682057
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:54:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:104]:ItemTipsSimpleInfoView     CloseView
[logic/ui/CViewCtrl.lua:104]:ItemTipsSimpleInfoView     CloseView
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 6
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[logic/ui/CViewCtrl.lua:94]:CNpcShopView ShowView
[core/global.lua:59]:<color=#ffeb04>渠道: </color>
[core/global.lua:59]:<color=#ffeb04>包名: ylq</color>
[logic/ui/CViewBase.lua:125]:CNpcShopView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CNpcShopView     CloseView
[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682067
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:54:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[logic/ui/CViewCtrl.lua:94]:ItemTipsSimpleInfoView ShowView
[logic/ui/CViewBase.lua:125]:ItemTipsSimpleInfoView LoadDone!
[logic/ui/CViewCtrl.lua:104]:ItemTipsSimpleInfoView     CloseView
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 6
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[core/table.lua:94]:<--Net Send: fuli.C2GSStartLuckDraw = {
|  type = 1
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawPos = {
|  cnt = 5
|  cost = 100
|  pos = 2
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682077
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:54:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: fuli.C2GSGiveLuckDraw = {}
[core/table.lua:94]:-->Net Receive: item.GS2CItemAmount = {
|  amount = 12
|  create_time = 1753682086
|  id = 4
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#B[深蓝琥珀] x 10#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 10
|  |  |  sid = 14021
|  |  |  virtual = 14021
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682087
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:54:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:104]:CLimitRewardView     CloseView
[logic/ui/CViewCtrl.lua:94]:CLimitRewardView ShowView
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[logic/ui/CViewBase.lua:125]:CLimitRewardView LoadDone!
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 5
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[logic/ui/CViewCtrl.lua:104]:CLimitRewardView     CloseView
[logic/ui/CViewCtrl.lua:94]:CForetellView ShowView
[logic/ui/CViewBase.lua:125]:CForetellView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682097
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:54:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CForetellView     CloseView
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 508
|  |  |  }
|  |  |  name = "魂夕"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 81
|  |  npctype = 5008
|  }
|  eid = 16
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 16
|  pos_info = {
|  |  x = 24000
|  |  y = 12900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 508
|  }
|  name = "魂夕"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1508
|  |  |  }
|  |  |  name = "小叶"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 5
|  |  npctype = 5041
|  }
|  eid = 2
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 2
|  pos_info = {
|  |  x = 40000
|  |  y = 14600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1508
|  }
|  name = "小叶"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 414
|  |  |  }
|  |  |  name = "蓝堂昼"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 75
|  |  npctype = 5006
|  }
|  eid = 14
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 14
|  pos_info = {
|  |  x = 28000
|  |  y = 14500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 414
|  }
|  name = "蓝堂昼"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 311
|  |  |  }
|  |  |  name = "花常酒"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 78
|  |  npctype = 5007
|  }
|  eid = 15
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 15
|  pos_info = {
|  |  x = 35600
|  |  y = 13400
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 311
|  }
|  name = "花常酒"
|  trapmine = {}
}
[logic/ui/CViewCtrl.lua:94]:COnlineGiftView ShowView
[logic/ui/CViewBase.lua:125]:COnlineGiftView LoadDone!
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 16
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 2
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 14
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 15
|  scene_id = 12
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 23061
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGetOnlineGift = {
|  rewardid = 1
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 23061
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COnlineGiftStatus = {
|  status = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COnlineGift = {
|  onlinetime = 365
|  reward = {
|  |  [1] = {
|  |  |  random_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 1
|  |  |  |  |  sid = 27402
|  |  |  |  }
|  |  |  }
|  |  |  rewardid = 1
|  |  |  stable_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 2
|  |  |  |  |  sid = 10030
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  amount = 1
|  |  |  |  |  sid = 1002
|  |  |  |  }
|  |  |  }
|  |  }
|  }
|  status = 1
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  coin = 65280
|  |  mask = "20"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  coin = 65280
}
[core/table.lua:94]:-->Net Receive: item.GS2CItemAmount = {
|  amount = 7
|  create_time = 1753682104
|  id = 2
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得 #G[扫荡券] x 2#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: item.GS2CItemAmount = {
|  amount = 2
|  create_time = 1753682104
|  id = 8
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得 #P[梦觉书·低] x 1#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G1000#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshAchieveTask = {
|  info = {
|  |  achievetype = 1
|  |  degree = 1
|  |  describe = "领取1次在线奖励"
|  |  name = "在线奖励"
|  |  target = 1
|  |  taskid = 31001
|  }
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 1000
|  |  |  sid = 1002
|  |  |  virtual = 1002
|  |  }
|  |  [2] = {
|  |  |  amount = 1
|  |  |  sid = 27402
|  |  |  virtual = 27402
|  |  }
|  |  [3] = {
|  |  |  amount = 2
|  |  |  sid = 10030
|  |  |  virtual = 10030
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:104]:COnlineGiftView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682107
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:55:07
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CLimitRewardView ShowView
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[logic/ui/CViewBase.lua:125]:CLimitRewardView LoadDone!
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 5
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[logic/ui/CViewCtrl.lua:104]:CLimitRewardView     CloseView
[logic/ui/CViewCtrl.lua:94]:CLimitRewardView ShowView
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[logic/ui/CViewBase.lua:125]:CLimitRewardView LoadDone!
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 5
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 5
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[logic/ui/CViewCtrl.lua:104]:CLimitRewardView     CloseView
[logic/ui/CViewCtrl.lua:94]:CFirstChargeView ShowView
[logic/ui/CViewBase.lua:125]:CFirstChargeView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CFirstChargeView     CloseView
[logic/ui/CViewCtrl.lua:94]:CLimitRewardView ShowView
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[logic/ui/CViewBase.lua:125]:CLimitRewardView LoadDone!
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 5
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[logic/ui/CViewCtrl.lua:104]:CLimitRewardView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682117
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:55:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Tips_LoginSevenDay_1"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 5000301
|  }
}
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Tips_LoginSevenDay"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 5003099
|  }
}
[logic/ui/CViewCtrl.lua:94]:CLoginRewardView ShowView
[logic/ui/CViewBase.lua:125]:CLoginRewardView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 23026
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGetLoginReward = {
|  day = 3
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 23026
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardDay = {
|  rewarded_day = 108
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得 #O[50级橙武礼包] x 1#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1753682119
|  |  id = 16
|  |  itemlevel = 4
|  |  name = "50级橙武礼包"
|  |  sid = 12083
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CItemQuickUse = {
|  id = 16
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  sid = 12083
|  |  |  virtual = 12083
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 23026
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGetLoginReward = {
|  day = 1
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 23026
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardDay = {
|  rewarded_day = 109
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  coin = 565280
|  |  mask = "20"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  coin = 565280
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 500000
|  |  |  sid = 1002
|  |  |  virtual = 1002
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G500000#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveDone = {
|  id = 10301
|  pop = true
}
[logic/ui/CViewCtrl.lua:94]:CAchieveFinishTipsView ShowView
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 1
|  |  |  |  [2] = 2
|  |  |  }
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  blist = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  id = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveDone = {
|  id = 10302
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 1
|  |  |  |  [2] = 2
|  |  |  }
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  blist = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  id = 4
|  |  }
|  }
}
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewBase.lua:125]:CAchieveFinishTipsView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 13001
}
[core/table.lua:94]:<--Net Send: achieve.C2GSAchieveMain = {}
[logic/ui/CViewCtrl.lua:104]:CAchieveFinishTipsView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 13001
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveMain = {
|  directions = {
|  |  [1] = {
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  id = 2
|  |  }
|  |  [3] = {
|  |  |  id = 3
|  |  }
|  |  [4] = {
|  |  |  id = 4
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:94]:CAchieveMainView ShowView
[logic/ui/CViewBase.lua:125]:CAchieveMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 13002
}
[core/table.lua:94]:<--Net Send: achieve.C2GSAchieveDirection = {
|  belong = 2
|  id = 1
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 13002
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveDirection = {
|  achlist = {
|  |  [1] = {
|  |  |  cur = 31000
|  |  |  id = 10401
|  |  }
|  |  [10] = {
|  |  |  cur = 31000
|  |  |  id = 10410
|  |  }
|  |  [11] = {
|  |  |  cur = 31000
|  |  |  id = 10411
|  |  }
|  |  [12] = {
|  |  |  cur = 31000
|  |  |  id = 10412
|  |  }
|  |  [13] = {
|  |  |  cur = 596280
|  |  |  done = 1
|  |  |  id = 10301
|  |  }
|  |  [14] = {
|  |  |  cur = 596280
|  |  |  done = 1
|  |  |  id = 10302
|  |  }
|  |  [15] = {
|  |  |  cur = 596280
|  |  |  id = 10303
|  |  }
|  |  [16] = {
|  |  |  cur = 596280
|  |  |  id = 10304
|  |  }
|  |  [17] = {
|  |  |  cur = 596280
|  |  |  id = 10305
|  |  }
|  |  [18] = {
|  |  |  cur = 596280
|  |  |  id = 10306
|  |  }
|  |  [19] = {
|  |  |  cur = 596280
|  |  |  id = 10307
|  |  }
|  |  [2] = {
|  |  |  cur = 31000
|  |  |  id = 10402
|  |  }
|  |  [20] = {
|  |  |  cur = 596280
|  |  |  id = 10308
|  |  }
|  |  [21] = {
|  |  |  cur = 596280
|  |  |  id = 10309
|  |  }
|  |  [22] = {
|  |  |  cur = 596280
|  |  |  id = 10310
|  |  }
|  |  [23] = {
|  |  |  cur = 596280
|  |  |  id = 10311
|  |  }
|  |  [24] = {
|  |  |  cur = 596280
|  |  |  id = 10312
|  |  }
|  |  [3] = {
|  |  |  cur = 31000
|  |  |  id = 10403
|  |  }
|  |  [4] = {
|  |  |  cur = 31000
|  |  |  id = 10404
|  |  }
|  |  [5] = {
|  |  |  cur = 31000
|  |  |  id = 10405
|  |  }
|  |  [6] = {
|  |  |  cur = 31000
|  |  |  id = 10406
|  |  }
|  |  [7] = {
|  |  |  cur = 31000
|  |  |  id = 10407
|  |  }
|  |  [8] = {
|  |  |  cur = 31000
|  |  |  id = 10408
|  |  }
|  |  [9] = {
|  |  |  cur = 31000
|  |  |  id = 10409
|  |  }
|  }
|  belong = 2
|  id = 1
}
[core/table.lua:94]:<--Net Send: achieve.C2GSCloseMainUI = {}
[logic/ui/CViewCtrl.lua:104]:CAchieveMainView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682127
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:55:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682137
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:55:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682147
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:55:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 23026
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGetLoginReward = {
|  day = 2
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 23026
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardDay = {
|  rewarded_day = 111
}
[core/table.lua:94]:-->Net Receive: partner.GS2CAddPartnerList = {
|  partner_list = {
|  |  [1] = {
|  |  |  abnormal_attr_ratio = 600
|  |  |  amount = 1
|  |  |  attack = 273
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 67
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  grade = 1
|  |  |  mask = "fffffefe"
|  |  |  max_hp = 1862
|  |  |  model_info = {
|  |  |  |  shape = 313
|  |  |  |  skin = 203130
|  |  |  }
|  |  |  name = "檀"
|  |  |  parid = 4
|  |  |  partner_type = 313
|  |  |  patahp = 1862
|  |  |  power = 879
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 700
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31301
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31302
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31303
|  |  |  |  }
|  |  |  }
|  |  |  speed = 665
|  |  |  star = 2
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CShowNewPartnerUI = {
|  par_types = {
|  |  [1] = {
|  |  |  desc = "恭喜你获得伙伴：檀"
|  |  |  par_type = 313
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:94]:CPartnerGainView2 ShowView
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  sid = 313
|  |  |  virtual = 1010
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得[#P檀#n]"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 313
|  |  |  |  value = 1
|  |  |  }
|  |  }
|  |  id = 3
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 313
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 313
|  |  |  |  value = 1
|  |  |  }
|  |  }
|  |  id = 3
|  }
}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshPartnerTask = {
|  partnertask_progress = {
|  |  [1] = {
|  |  |  parid = 313
|  |  |  status = 1
|  |  |  taskid = 62195
|  |  }
|  }
|  refresh_id = 1
}
[core/table.lua:94]:-->Net Receive: handbook.GS2CHandBookRedPoint = {
|  red_point = {
|  |  book_type = 1
|  |  red_point = 1
|  }
}
[core/table.lua:94]:-->Net Receive: handbook.GS2CBookInfoChange = {
|  book_info = {
|  |  chapter = {
|  |  |  [1] = {
|  |  |  |  id = 313021
|  |  |  |  unlock = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = {
|  |  |  |  |  [1] = 113023
|  |  |  |  }
|  |  |  |  id = 313022
|  |  |  |  unlock = 1
|  |  |  }
|  |  }
|  |  condition = {
|  |  |  [1] = 113021
|  |  }
|  |  entry_name = 1
|  |  id = 100302
|  |  red_point = 3
|  |  repair = 1
|  |  show = 1
|  |  unlock = 1
|  }
}
[core/table.lua:94]:-->Net Receive: handbook.GS2CHandBookRedPoint = {
|  red_point = {
|  |  book_type = 1
|  |  red_point = 1
|  }
}
[core/table.lua:94]:-->Net Receive: handbook.GS2CBookInfoChange = {
|  book_info = {
|  |  chapter = {
|  |  |  [1] = {
|  |  |  |  id = 313131
|  |  |  }
|  |  }
|  |  condition = {
|  |  |  [1] = 113131
|  |  }
|  |  entry_name = 1
|  |  id = 100313
|  |  red_point = 1
|  |  repair = 1
|  |  show = 1
|  |  unlock = 1
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 313
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 313
|  |  |  |  value = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 313
|  |  |  |  value = 1
|  |  |  }
|  |  }
|  |  id = 3
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveDone = {
|  id = 11901
|  pop = true
}
[logic/ui/CViewCtrl.lua:94]:CAchieveFinishTipsView ShowView
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 1
|  |  |  |  [2] = 2
|  |  |  |  [3] = 3
|  |  |  }
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  blist = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  id = 4
|  |  }
|  }
}
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewBase.lua:125]:CAchieveFinishTipsView LoadDone!
[logic/ui/CViewBase.lua:125]:CPartnerGainView2 LoadDone!
[logic/ui/CViewCtrl.lua:104]:CPartnerGainView2     CloseView
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682157
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:55:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 23026
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGetLoginReward = {
|  day = 5
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 23026
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardDay = {
|  rewarded_day = 127
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得 #P[5级宝石礼包] x 1#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1753682157
|  |  id = 17
|  |  itemlevel = 3
|  |  name = "5级宝石礼包"
|  |  sid = 13002
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CItemQuickUse = {
|  id = 17
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  sid = 13002
|  |  |  virtual = 13002
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CItemQuickUseView ShowView
[logic/ui/CViewBase.lua:125]:CItemQuickUseView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CAchieveFinishTipsView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 5001
}
[core/table.lua:94]:<--Net Send: item.C2GSItemUse = {
|  amount = 1
|  itemid = 17
|  target = 10008
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 5001
}
[core/table.lua:94]:-->Net Receive: item.GS2CItemAmount = {
|  create_time = 1753682157
|  id = 17
}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1753682159
|  |  id = 18
|  |  itemlevel = 2
|  |  name = "5级疾风宝石"
|  |  sid = 18504
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1753682159
|  |  id = 19
|  |  itemlevel = 2
|  |  name = "5级翠星宝石"
|  |  sid = 18404
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1753682159
|  |  id = 20
|  |  itemlevel = 2
|  |  name = "5级黄金宝石"
|  |  sid = 18304
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1753682159
|  |  id = 21
|  |  itemlevel = 2
|  |  name = "5级绯红宝石"
|  |  sid = 18004
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1753682159
|  |  id = 22
|  |  itemlevel = 2
|  |  name = "5级双生宝石"
|  |  sid = 18204
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1753682159
|  |  id = 23
|  |  itemlevel = 2
|  |  name = "5级八云宝石"
|  |  sid = 18104
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#B[5级疾风宝石] x 1#n，#B[5级翠星宝石] x 1#n，#B[5级黄金宝石] x 1#n，#B[5级绯红宝石] x 1#n，#B[5级双生宝石] x 1#n，#B[5级八云宝石] x 1#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  sid = 18504
|  |  |  virtual = 18504
|  |  }
|  |  [2] = {
|  |  |  amount = 1
|  |  |  sid = 18104
|  |  |  virtual = 18104
|  |  }
|  |  [3] = {
|  |  |  amount = 1
|  |  |  sid = 18004
|  |  |  virtual = 18004
|  |  }
|  |  [4] = {
|  |  |  amount = 1
|  |  |  sid = 18204
|  |  |  virtual = 18204
|  |  }
|  |  [5] = {
|  |  |  amount = 1
|  |  |  sid = 18404
|  |  |  virtual = 18404
|  |  }
|  |  [6] = {
|  |  |  amount = 1
|  |  |  sid = 18304
|  |  |  virtual = 18304
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemQuickUseView     CloseView
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682167
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:56:07
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682177
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:56:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CLoginRewardView     CloseView
[logic/ui/CViewCtrl.lua:94]:CLimitRewardView ShowView
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[logic/ui/CViewBase.lua:125]:CLimitRewardView LoadDone!
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 5
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[logic/ui/CViewCtrl.lua:104]:CLimitRewardView     CloseView
[logic/ui/CViewCtrl.lua:94]:CWelfareView ShowView
[logic/ui/CViewBase.lua:125]:CWelfareView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CItemPartnertSelectPackageView ShowView
[logic/ui/CViewBase.lua:125]:CItemPartnertSelectPackageView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemPartnertSelectPackageView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682187
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:56:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:ItemTipsSimpleInfoView ShowView
[logic/ui/CViewBase.lua:125]:ItemTipsSimpleInfoView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CItemPartnertSelectPackageView ShowView
[logic/ui/CViewCtrl.lua:104]:ItemTipsSimpleInfoView     CloseView
[logic/ui/CViewBase.lua:125]:CItemPartnertSelectPackageView LoadDone!
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[logic/ui/CViewCtrl.lua:104]:CItemPartnertSelectPackageView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682197
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:56:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CItemPartnertSelectPackageView ShowView
[logic/ui/CViewBase.lua:125]:CItemPartnertSelectPackageView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemPartnertSelectPackageView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemPartnertSelectPackageView ShowView
[logic/ui/CViewBase.lua:125]:CItemPartnertSelectPackageView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682207
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:56:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682217
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:56:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CItemPartnertSelectPackageView     CloseView
[core/table.lua:94]:<--Net Send: huodong.C2GSReceiveEnergy = {
|  index = 1
}
[core/table.lua:94]:-->Net Receive: player.GS2CTodayInfo = {
|  info = {
|  |  energy_receive = 1
|  |  mask = "8"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: todyInfo = {
|  energy_receive = 1
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  energy = 163
|  |  mask = "100000000000000"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  energy = 163
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G40#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G40#n"
|  type = 6
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682227
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:57:07
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CWelfareView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemBagMainView ShowView
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CViewBase.lua:125]:CItemBagMainView LoadDone!
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CPopupBox.lua:148]:index ..1
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682237
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:57:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CViewCtrl.lua:94]:CItemTipsBaseInfoView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsBaseInfoView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemTipsBaseInfoView     CloseView
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CViewCtrl.lua:104]:CItemBagMainView     CloseView
[logic/ui/CViewCtrl.lua:94]:CLimitRewardView ShowView
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[logic/ui/CViewBase.lua:125]:CLimitRewardView LoadDone!
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 5
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[core/table.lua:94]:<--Net Send: fuli.C2GSStartLuckDraw = {
|  type = 1
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawPos = {
|  cnt = 4
|  cost = 100
|  pos = 8
}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682247
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:57:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 4
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 4
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[core/table.lua:94]:<--Net Send: fuli.C2GSGiveLuckDraw = {}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1753682256
|  |  id = 24
|  |  itemlevel = 3
|  |  name = "魂夕碎片"
|  |  sid = 20508
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#P[魂夕碎片] x 1#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  sid = 20508
|  |  |  virtual = 20508
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682257
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:57:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:ItemTipsSimpleInfoView ShowView
[logic/ui/CViewBase.lua:125]:ItemTipsSimpleInfoView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:104]:ItemTipsSimpleInfoView     CloseView
[logic/ui/CViewCtrl.lua:104]:ItemTipsSimpleInfoView     CloseView
[core/table.lua:94]:<--Net Send: fuli.C2GSStartLuckDraw = {
|  type = 1
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawPos = {
|  cnt = 3
|  cost = 100
|  pos = 8
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682267
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:57:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: fuli.C2GSGiveLuckDraw = {}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1753682269
|  |  id = 25
|  |  itemlevel = 3
|  |  name = "松姑子碎片"
|  |  sid = 20417
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#P[松姑子碎片] x 1#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  sid = 20417
|  |  |  virtual = 20417
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682277
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:57:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:104]:CLimitRewardView     CloseView
[logic/ui/CViewCtrl.lua:94]:CLimitRewardView ShowView
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[logic/ui/CViewBase.lua:125]:CLimitRewardView LoadDone!
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 3
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[logic/ui/CViewCtrl.lua:104]:CLimitRewardView     CloseView
[logic/ui/CViewCtrl.lua:94]:CFirstChargeView ShowView
[logic/ui/CViewBase.lua:125]:CFirstChargeView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682287
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:58:07
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CFirstChargeView     CloseView
[logic/ui/CViewCtrl.lua:94]:CFirstChargeView ShowView
[logic/ui/CViewBase.lua:125]:CFirstChargeView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682297
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:58:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682307
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:58:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682317
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:58:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682327
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:58:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682337
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:58:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682347
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:59:07
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682357
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:59:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682367
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:59:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682377
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:59:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CFirstChargeView     CloseView
[logic/ui/CViewCtrl.lua:94]:CLimitRewardView ShowView
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[logic/ui/CViewBase.lua:125]:CLimitRewardView LoadDone!
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 3
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682387
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:59:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CLimitRewardView     CloseView
[logic/ui/CViewCtrl.lua:94]:COnlineGiftView ShowView
[logic/ui/CViewBase.lua:125]:COnlineGiftView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 23061
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGetOnlineGift = {
|  rewardid = 2
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 23061
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COnlineGiftStatus = {
|  status = 3
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COnlineGift = {
|  onlinetime = 651
|  reward = {
|  |  [1] = {
|  |  |  random_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 1
|  |  |  |  |  sid = 27402
|  |  |  |  }
|  |  |  }
|  |  |  rewardid = 1
|  |  |  stable_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 2
|  |  |  |  |  sid = 10030
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  amount = 1
|  |  |  |  |  sid = 1002
|  |  |  |  }
|  |  |  }
|  |  }
|  |  [2] = {
|  |  |  random_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 1
|  |  |  |  |  sid = 18002
|  |  |  |  }
|  |  |  }
|  |  |  rewardid = 2
|  |  |  stable_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 3
|  |  |  |  |  sid = 10030
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  amount = 1
|  |  |  |  |  sid = 1002
|  |  |  |  }
|  |  |  }
|  |  }
|  }
|  status = 3
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  coin = 568280
|  |  mask = "20"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  coin = 568280
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G3000#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshAchieveTask = {
|  info = {
|  |  achievetype = 1
|  |  degree = 1
|  |  describe = "领取1次在线奖励"
|  |  name = "在线奖励"
|  |  target = 1
|  |  taskid = 31001
|  }
}
[core/table.lua:94]:-->Net Receive: item.GS2CItemAmount = {
|  amount = 10
|  create_time = 1753682390
|  id = 2
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得 #G[扫荡券] x 3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: item.GS2CItemAmount = {
|  amount = 2
|  create_time = 1753682390
|  id = 6
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得 #B[3级绯红宝石] x 1#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 3000
|  |  |  sid = 1002
|  |  |  virtual = 1002
|  |  }
|  |  [2] = {
|  |  |  amount = 1
|  |  |  sid = 18002
|  |  |  virtual = 18002
|  |  }
|  |  [3] = {
|  |  |  amount = 3
|  |  |  sid = 10030
|  |  |  virtual = 10030
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682397
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 13:59:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  mask = "10000000"
|  |  power_rank = 2
|  }
|  partnerid = 4
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  power_rank = 2
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHeroBoxRecord = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeScore = {
|  cur_id = 1
|  end_time = 1752505139
|  start_time = 1751209200
}
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "天降宝物，世界各地出现了大量的灵魂宝箱，欢迎冒险者们前去寻宝"
|  grade = 28
|  horse_race = 1
}
[logic/ui/CViewCtrl.lua:104]:COnlineGiftView     CloseView
[logic/ui/CViewCtrl.lua:94]:CLoginRewardView ShowView
[logic/ui/CViewBase.lua:125]:CLoginRewardView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682407
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:00:07
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CLoginRewardView     CloseView
[logic/ui/CViewCtrl.lua:94]:CPartnerHireView ShowView
[logic/ui/CViewBase.lua:125]:CPartnerHireView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682417
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:00:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682427
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:00:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: huodong.C2GSHirePartner = {
|  parid = 501
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRefreshHireInfo = {
|  parid = 501
|  times = 1
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  coin = 558280
|  |  mask = "20"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  coin = 558280
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "消耗了#w1#R10000#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "消耗了#w1#R10000#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得[#P阿坊#n]"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得[#P阿坊#n]"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: partner.GS2CAddPartnerList = {
|  partner_list = {
|  |  [1] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 294
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 1000
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 50
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  grade = 1
|  |  |  mask = "fffffefe"
|  |  |  max_hp = 1753
|  |  |  model_info = {
|  |  |  |  shape = 501
|  |  |  |  skin = 205010
|  |  |  }
|  |  |  name = "阿坊"
|  |  |  parid = 5
|  |  |  partner_type = 501
|  |  |  patahp = 1753
|  |  |  power = 651
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 400
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50101
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50102
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50103
|  |  |  |  }
|  |  |  }
|  |  |  speed = 70
|  |  |  star = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CShowNewPartnerUI = {
|  par_types = {
|  |  [1] = {
|  |  |  desc = "恭喜你获得伙伴：阿坊"
|  |  |  par_type = 501
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:94]:CPartnerGainView2 ShowView
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 502
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 502
|  |  |  |  value = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 501
|  |  |  |  value = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 502
|  |  |  |  value = 7
|  |  |  }
|  |  }
|  |  id = 1
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 502
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 501
|  |  |  |  value = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 502
|  |  |  |  value = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 501
|  |  |  |  value = 1
|  |  |  }
|  |  |  [5] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 502
|  |  |  |  value = 7
|  |  |  }
|  |  }
|  |  id = 1
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 502
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 501
|  |  |  |  value = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 502
|  |  |  |  value = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 501
|  |  |  |  value = 1
|  |  |  }
|  |  |  [5] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 502
|  |  |  |  value = 7
|  |  |  }
|  |  |  [6] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 501
|  |  |  |  value = 1
|  |  |  }
|  |  }
|  |  id = 1
|  }
}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshPartnerTask = {
|  partnertask_progress = {
|  |  [1] = {
|  |  |  parid = 501
|  |  |  status = 1
|  |  |  taskid = 62085
|  |  }
|  }
|  refresh_id = 1
}
[core/table.lua:94]:-->Net Receive: handbook.GS2CHandBookRedPoint = {
|  red_point = {
|  |  book_type = 1
|  |  red_point = 1
|  }
}
[core/table.lua:94]:-->Net Receive: handbook.GS2CBookInfoChange = {
|  book_info = {
|  |  chapter = {
|  |  |  [1] = {
|  |  |  |  id = 315011
|  |  |  }
|  |  |  [2] = {
|  |  |  |  id = 315012
|  |  |  }
|  |  }
|  |  condition = {
|  |  |  [1] = 115011
|  |  }
|  |  entry_name = 1
|  |  id = 100501
|  |  red_point = 1
|  |  repair = 1
|  |  show = 1
|  |  unlock = 1
|  }
}
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_ZhaoMu_Three"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1005099
|  }
}
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_DrawCard_Three"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 3012099
|  }
}
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_DrawCardLineUp_Three_PartnerMain"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 3013099
|  }
}
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Partner_HBPY_MainMenu"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 3014099
|  }
}
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Partner_HPPY_PartnerMain"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 3015099
|  }
}
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Get3Item14001"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 7000071
|  }
}
[logic/ui/CViewBase.lua:125]:CPartnerGainView2 LoadDone!
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 3
|  |  create_time = 1753682427
|  |  id = 26
|  |  itemlevel = 2
|  |  name = "鲜肉包"
|  |  sid = 14001
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#B[鲜肉包] x 3#n"
|  type = 6
}
[logic/ui/CViewCtrl.lua:104]:CPartnerGainView2     CloseView
[logic/ui/CViewCtrl.lua:104]:CPartnerHireView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemBagMainView ShowView
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CViewBase.lua:125]:CItemBagMainView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CItemTipsBaseInfoView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsBaseInfoView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemTipsBaseInfoView     CloseView
[logic/ui/CPopupBox.lua:148]:index ..1
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682437
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:00:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CViewCtrl.lua:94]:CItemTipsBaseInfoView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsBaseInfoView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemTipsBaseInfoView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemTipsBaseInfoView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsBaseInfoView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682447
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:00:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 15004
}
[core/table.lua:94]:<--Net Send: store.C2GSOpenShop = {
|  shop_id = 204
}
[logic/ui/CViewCtrl.lua:104]:CItemBagMainView     CloseView
[logic/ui/CViewCtrl.lua:104]:CItemTipsBaseInfoView     CloseView
[logic/ui/CViewCtrl.lua:104]:CItemTipsBaseInfoView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 15004
}
[core/table.lua:94]:-->Net Receive: store.GS2CNpcStoreInfo = {
|  goodslist = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  item_id = 204044
|  |  |  limit = 1
|  |  |  pos = 1
|  |  }
|  |  [10] = {
|  |  |  amount = 1
|  |  |  item_id = 204012
|  |  |  limit = 1
|  |  |  pos = 10
|  |  }
|  |  [2] = {
|  |  |  amount = 1
|  |  |  item_id = 204029
|  |  |  limit = 1
|  |  |  pos = 2
|  |  }
|  |  [3] = {
|  |  |  amount = 1
|  |  |  item_id = 204030
|  |  |  limit = 1
|  |  |  pos = 3
|  |  }
|  |  [4] = {
|  |  |  amount = 1
|  |  |  item_id = 204041
|  |  |  limit = 1
|  |  |  pos = 4
|  |  }
|  |  [5] = {
|  |  |  amount = 1
|  |  |  item_id = 204029
|  |  |  limit = 1
|  |  |  pos = 5
|  |  }
|  |  [6] = {
|  |  |  amount = 1
|  |  |  item_id = 204026
|  |  |  limit = 1
|  |  |  pos = 6
|  |  }
|  |  [7] = {
|  |  |  amount = 1
|  |  |  item_id = 204044
|  |  |  limit = 1
|  |  |  pos = 7
|  |  }
|  |  [8] = {
|  |  |  amount = 1
|  |  |  item_id = 204046
|  |  |  limit = 1
|  |  |  pos = 8
|  |  }
|  |  [9] = {
|  |  |  amount = 1
|  |  |  item_id = 204012
|  |  |  limit = 1
|  |  |  pos = 9
|  |  }
|  }
|  refresh_coin_type = 3
|  refresh_count = 9999
|  refresh_rule = 2
|  shop_id = 204
}
[logic/ui/CViewCtrl.lua:94]:CNpcShopView ShowView
[core/global.lua:59]:<color=#ffeb04>渠道: </color>
[core/global.lua:59]:<color=#ffeb04>包名: ylq</color>
[logic/ui/CViewBase.lua:125]:CNpcShopView LoadDone!
[core/table.lua:94]:-->Net Receive: store.GS2CNpcStoreInfo = {
|  goodslist = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  item_id = 204044
|  |  |  limit = 1
|  |  |  pos = 1
|  |  }
|  |  [10] = {
|  |  |  amount = 1
|  |  |  item_id = 204012
|  |  |  limit = 1
|  |  |  pos = 10
|  |  }
|  |  [2] = {
|  |  |  amount = 1
|  |  |  item_id = 204029
|  |  |  limit = 1
|  |  |  pos = 2
|  |  }
|  |  [3] = {
|  |  |  amount = 1
|  |  |  item_id = 204030
|  |  |  limit = 1
|  |  |  pos = 3
|  |  }
|  |  [4] = {
|  |  |  amount = 1
|  |  |  item_id = 204041
|  |  |  limit = 1
|  |  |  pos = 4
|  |  }
|  |  [5] = {
|  |  |  amount = 1
|  |  |  item_id = 204029
|  |  |  limit = 1
|  |  |  pos = 5
|  |  }
|  |  [6] = {
|  |  |  amount = 1
|  |  |  item_id = 204026
|  |  |  limit = 1
|  |  |  pos = 6
|  |  }
|  |  [7] = {
|  |  |  amount = 1
|  |  |  item_id = 204044
|  |  |  limit = 1
|  |  |  pos = 7
|  |  }
|  |  [8] = {
|  |  |  amount = 1
|  |  |  item_id = 204046
|  |  |  limit = 1
|  |  |  pos = 8
|  |  }
|  |  [9] = {
|  |  |  amount = 1
|  |  |  item_id = 204012
|  |  |  limit = 1
|  |  |  pos = 9
|  |  }
|  }
|  refresh_coin_type = 3
|  refresh_count = 9999
|  refresh_rule = 2
|  shop_id = 204
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 15004
}
[core/table.lua:94]:<--Net Send: store.C2GSOpenShop = {
|  shop_id = 205
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 15004
}
[core/table.lua:94]:-->Net Receive: store.GS2CNpcStoreInfo = {
|  goodslist = {
|  |  [1] = {
|  |  |  amount = 5
|  |  |  item_id = 205001
|  |  |  limit = 1
|  |  |  pos = 1
|  |  }
|  |  [10] = {
|  |  |  amount = 4294967295
|  |  |  item_id = 205010
|  |  |  pos = 10
|  |  }
|  |  [11] = {
|  |  |  amount = 4294967295
|  |  |  item_id = 205011
|  |  |  pos = 11
|  |  }
|  |  [12] = {
|  |  |  amount = 4294967295
|  |  |  item_id = 205012
|  |  |  pos = 12
|  |  }
|  |  [13] = {
|  |  |  amount = 4294967295
|  |  |  item_id = 205013
|  |  |  pos = 13
|  |  }
|  |  [14] = {
|  |  |  amount = 4294967295
|  |  |  item_id = 205014
|  |  |  pos = 14
|  |  }
|  |  [15] = {
|  |  |  amount = 4294967295
|  |  |  item_id = 205015
|  |  |  pos = 15
|  |  }
|  |  [2] = {
|  |  |  amount = 4294967295
|  |  |  item_id = 205002
|  |  |  pos = 2
|  |  }
|  |  [3] = {
|  |  |  amount = 4294967295
|  |  |  item_id = 205003
|  |  |  pos = 3
|  |  }
|  |  [4] = {
|  |  |  amount = 4294967295
|  |  |  item_id = 205004
|  |  |  pos = 4
|  |  }
|  |  [5] = {
|  |  |  amount = 4294967295
|  |  |  item_id = 205005
|  |  |  pos = 5
|  |  }
|  |  [6] = {
|  |  |  amount = 4294967295
|  |  |  item_id = 205006
|  |  |  pos = 6
|  |  }
|  |  [7] = {
|  |  |  amount = 4294967295
|  |  |  item_id = 205007
|  |  |  pos = 7
|  |  }
|  |  [8] = {
|  |  |  amount = 4294967295
|  |  |  item_id = 205008
|  |  |  pos = 8
|  |  }
|  |  [9] = {
|  |  |  amount = 4294967295
|  |  |  item_id = 205009
|  |  |  pos = 9
|  |  }
|  }
|  shop_id = 205
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 15004
}
[core/table.lua:94]:<--Net Send: store.C2GSOpenShop = {
|  shop_id = 204
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 15004
}
[core/table.lua:94]:-->Net Receive: store.GS2CNpcStoreInfo = {
|  goodslist = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  item_id = 204044
|  |  |  limit = 1
|  |  |  pos = 1
|  |  }
|  |  [10] = {
|  |  |  amount = 1
|  |  |  item_id = 204012
|  |  |  limit = 1
|  |  |  pos = 10
|  |  }
|  |  [2] = {
|  |  |  amount = 1
|  |  |  item_id = 204029
|  |  |  limit = 1
|  |  |  pos = 2
|  |  }
|  |  [3] = {
|  |  |  amount = 1
|  |  |  item_id = 204030
|  |  |  limit = 1
|  |  |  pos = 3
|  |  }
|  |  [4] = {
|  |  |  amount = 1
|  |  |  item_id = 204041
|  |  |  limit = 1
|  |  |  pos = 4
|  |  }
|  |  [5] = {
|  |  |  amount = 1
|  |  |  item_id = 204029
|  |  |  limit = 1
|  |  |  pos = 5
|  |  }
|  |  [6] = {
|  |  |  amount = 1
|  |  |  item_id = 204026
|  |  |  limit = 1
|  |  |  pos = 6
|  |  }
|  |  [7] = {
|  |  |  amount = 1
|  |  |  item_id = 204044
|  |  |  limit = 1
|  |  |  pos = 7
|  |  }
|  |  [8] = {
|  |  |  amount = 1
|  |  |  item_id = 204046
|  |  |  limit = 1
|  |  |  pos = 8
|  |  }
|  |  [9] = {
|  |  |  amount = 1
|  |  |  item_id = 204012
|  |  |  limit = 1
|  |  |  pos = 9
|  |  }
|  }
|  refresh_coin_type = 3
|  refresh_count = 9999
|  refresh_rule = 2
|  shop_id = 204
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682457
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:00:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CNpcShopView     CloseView
[logic/ui/CViewCtrl.lua:94]:CLimitRewardView ShowView
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[logic/ui/CViewBase.lua:125]:CLimitRewardView LoadDone!
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 3
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[core/table.lua:94]:<--Net Send: fuli.C2GSStartLuckDraw = {
|  type = 1
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawPos = {
|  cnt = 2
|  cost = 100
|  pos = 4
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682467
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:01:07
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: fuli.C2GSGiveLuckDraw = {}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  coin = 758280
|  |  mask = "20"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  coin = 758280
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G200000#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 200000
|  |  |  sid = 1002
|  |  |  virtual = 1002
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:104]:CLimitRewardView     CloseView
[logic/ui/CViewCtrl.lua:94]:CPartnerMainView ShowView
[logic/ui/CViewBase.lua:125]:CPartnerMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682477
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:01:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CBaseBuyItemView ShowView
[logic/ui/CViewBase.lua:125]:CBaseBuyItemView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CBaseBuyItemView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682487
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:01:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CItemTipsMainView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsMainView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemTipsMainView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemTipsMainView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsMainView LoadDone!
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682497
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:01:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CItemTipsMainView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemTipsMainView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsMainView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemTipsMainView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemTipsMainView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682507
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:01:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CItemTipsMainView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemTipsMainView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsMainView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemTipsMainView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemTipsMainView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsMainView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemTipsMainView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682517
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:01:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CPartnerMainView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682527
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:02:07
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CMainMenuOperateView ShowView
[logic/ui/CViewBase.lua:125]:CMainMenuOperateView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CSysSettingView ShowView
[logic/ui/CViewBase.lua:125]:CSysSettingView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CMainMenuOperateView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682537
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:02:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CSysSettingView     CloseView
[logic/ui/CViewCtrl.lua:94]:CChapterFuBenMainView ShowView
[logic/ui/CViewBase.lua:125]:CChapterFuBenMainView LoadDone!
[core/global.lua:59]:<color=#ffeb04>剧情章节： 1</color>
[core/table.lua:94]:<--Net Send: huodong.C2GSGetChapterInfo = {
|  chapter = 1
|  type = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChapterInfo = {
|  info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 4
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = 1
|  |  |  level = 2
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  chapter = 1
|  |  |  level = 6
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  chapter = 1
|  |  |  level = 3
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  chapter = 1
|  |  |  level = 1
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  chapter = 1
|  |  |  level = 5
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [7] = {
|  |  |  chapter = 1
|  |  |  level = 7
|  |  |  open = 1
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-刷新章节信息 = {
|  [1] = {
|  |  chapter = 1
|  |  level = 1
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [2] = {
|  |  chapter = 1
|  |  level = 2
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [3] = {
|  |  chapter = 1
|  |  level = 3
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [4] = {
|  |  chapter = 1
|  |  level = 4
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [5] = {
|  |  chapter = 1
|  |  level = 5
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [6] = {
|  |  chapter = 1
|  |  level = 6
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [7] = {
|  |  chapter = 1
|  |  level = 7
|  |  open = 1
|  |  type = 1
|  }
}
[core/table.lua:94]:-刷新关卡信息 = {
|  chapter = 1
|  level = 1
|  open = 1
|  pass = 1
|  star = 3
|  star_condition = {
|  |  [1] = {
|  |  |  condition = "战斗胜利"
|  |  |  reach = 1
|  |  }
|  |  [2] = {
|  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  reach = 1
|  |  }
|  |  [3] = {
|  |  |  condition = "己方无阵亡单位"
|  |  |  reach = 1
|  |  }
|  }
|  type = 1
}
[core/table.lua:94]:-刷新关卡信息 = {
|  chapter = 1
|  level = 6
|  open = 1
|  pass = 1
|  star = 3
|  star_condition = {
|  |  [1] = {
|  |  |  condition = "战斗胜利"
|  |  |  reach = 1
|  |  }
|  |  [2] = {
|  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  reach = 1
|  |  }
|  |  [3] = {
|  |  |  condition = "己方无阵亡单位"
|  |  |  reach = 1
|  |  }
|  }
|  type = 1
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682547
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:02:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-刷新关卡信息 = {
|  chapter = 1
|  level = 2
|  open = 1
|  pass = 1
|  star = 3
|  star_condition = {
|  |  [1] = {
|  |  |  condition = "战斗胜利"
|  |  |  reach = 1
|  |  }
|  |  [2] = {
|  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  reach = 1
|  |  }
|  |  [3] = {
|  |  |  condition = "己方无阵亡单位"
|  |  |  reach = 1
|  |  }
|  }
|  type = 1
}
[core/table.lua:94]:-刷新关卡信息 = {
|  chapter = 1
|  level = 2
|  open = 1
|  pass = 1
|  star = 3
|  star_condition = {
|  |  [1] = {
|  |  |  condition = "战斗胜利"
|  |  |  reach = 1
|  |  }
|  |  [2] = {
|  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  reach = 1
|  |  }
|  |  [3] = {
|  |  |  condition = "己方无阵亡单位"
|  |  |  reach = 1
|  |  }
|  }
|  type = 1
}
[core/table.lua:94]:-刷新关卡信息 = {
|  chapter = 1
|  level = 4
|  open = 1
|  pass = 1
|  star = 3
|  star_condition = {
|  |  [1] = {
|  |  |  condition = "战斗胜利"
|  |  |  reach = 1
|  |  }
|  |  [2] = {
|  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  reach = 1
|  |  }
|  |  [3] = {
|  |  |  condition = "己方无阵亡单位"
|  |  |  reach = 1
|  |  }
|  }
|  type = 1
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682557
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:02:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-刷新关卡信息 = {
|  chapter = 1
|  level = 3
|  open = 1
|  pass = 1
|  star = 3
|  star_condition = {
|  |  [1] = {
|  |  |  condition = "战斗胜利"
|  |  |  reach = 1
|  |  }
|  |  [2] = {
|  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  reach = 1
|  |  }
|  |  [3] = {
|  |  |  condition = "己方无阵亡单位"
|  |  |  reach = 1
|  |  }
|  }
|  type = 1
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682567
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:02:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CChapterFuBenMainView     CloseView
[logic/ui/CViewCtrl.lua:94]:CChapterFuBenMainView ShowView
[logic/ui/CViewBase.lua:125]:CChapterFuBenMainView LoadDone!
[core/global.lua:59]:<color=#ffeb04>剧情章节： 1</color>
[core/table.lua:94]:<--Net Send: huodong.C2GSGetChapterInfo = {
|  chapter = 1
|  type = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChapterInfo = {
|  info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 4
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = 1
|  |  |  level = 2
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  chapter = 1
|  |  |  level = 6
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  chapter = 1
|  |  |  level = 3
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  chapter = 1
|  |  |  level = 1
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  chapter = 1
|  |  |  level = 5
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [7] = {
|  |  |  chapter = 1
|  |  |  level = 7
|  |  |  open = 1
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-刷新章节信息 = {
|  [1] = {
|  |  chapter = 1
|  |  level = 1
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [2] = {
|  |  chapter = 1
|  |  level = 2
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [3] = {
|  |  chapter = 1
|  |  level = 3
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [4] = {
|  |  chapter = 1
|  |  level = 4
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [5] = {
|  |  chapter = 1
|  |  level = 5
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [6] = {
|  |  chapter = 1
|  |  level = 6
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [7] = {
|  |  chapter = 1
|  |  level = 7
|  |  open = 1
|  |  type = 1
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682577
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:02:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682587
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:03:07
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682597
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:03:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CChapterFuBenMainView     CloseView
[logic/ui/CViewCtrl.lua:94]:CChapterFuBenMainView ShowView
[logic/ui/CViewBase.lua:125]:CChapterFuBenMainView LoadDone!
[core/global.lua:59]:<color=#ffeb04>剧情章节： 1</color>
[core/table.lua:94]:<--Net Send: huodong.C2GSGetChapterInfo = {
|  chapter = 1
|  type = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChapterInfo = {
|  info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 4
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = 1
|  |  |  level = 2
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  chapter = 1
|  |  |  level = 6
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  chapter = 1
|  |  |  level = 3
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  chapter = 1
|  |  |  level = 1
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  chapter = 1
|  |  |  level = 5
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [7] = {
|  |  |  chapter = 1
|  |  |  level = 7
|  |  |  open = 1
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-刷新章节信息 = {
|  [1] = {
|  |  chapter = 1
|  |  level = 1
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [2] = {
|  |  chapter = 1
|  |  level = 2
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [3] = {
|  |  chapter = 1
|  |  level = 3
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [4] = {
|  |  chapter = 1
|  |  level = 4
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [5] = {
|  |  chapter = 1
|  |  level = 5
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [6] = {
|  |  chapter = 1
|  |  level = 6
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [7] = {
|  |  chapter = 1
|  |  level = 7
|  |  open = 1
|  |  type = 1
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682607
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:03:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-刷新关卡信息 = {
|  chapter = 1
|  level = 2
|  open = 1
|  pass = 1
|  star = 3
|  star_condition = {
|  |  [1] = {
|  |  |  condition = "战斗胜利"
|  |  |  reach = 1
|  |  }
|  |  [2] = {
|  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  reach = 1
|  |  }
|  |  [3] = {
|  |  |  condition = "己方无阵亡单位"
|  |  |  reach = 1
|  |  }
|  }
|  type = 1
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682617
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:03:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-刷新关卡信息 = {
|  chapter = 1
|  level = 3
|  open = 1
|  pass = 1
|  star = 3
|  star_condition = {
|  |  [1] = {
|  |  |  condition = "战斗胜利"
|  |  |  reach = 1
|  |  }
|  |  [2] = {
|  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  reach = 1
|  |  }
|  |  [3] = {
|  |  |  condition = "己方无阵亡单位"
|  |  |  reach = 1
|  |  }
|  }
|  type = 1
}
[core/table.lua:94]:-刷新关卡信息 = {
|  chapter = 1
|  level = 5
|  open = 1
|  pass = 1
|  star = 3
|  star_condition = {
|  |  [1] = {
|  |  |  condition = "战斗胜利"
|  |  |  reach = 1
|  |  }
|  |  [2] = {
|  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  reach = 1
|  |  }
|  |  [3] = {
|  |  |  condition = "己方无阵亡单位"
|  |  |  reach = 1
|  |  }
|  }
|  type = 1
}
[core/table.lua:94]:-刷新关卡信息 = {
|  chapter = 1
|  level = 7
|  open = 1
|  type = 1
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682627
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:03:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>1 1 7 ---------------------</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 23067
}
[core/table.lua:94]:<--Net Send: huodong.C2GSFightChapterFb = {
|  chapter = 1
|  level = 7
|  type = 1
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 23067
}
[core/table.lua:94]:-->Net Receive: war.GS2CShowWar = {
|  lineup = "3"
|  war_id = 66
|  war_type = 17
}
[core/global.lua:59]:<color=#ffeb04>war_id: 66</color>
[core/global.lua:59]:<color=#ffeb04>CResCtrl.MoveToSecondary</color>
[core/global.lua:59]:<color=#ffeb04>CViewCtrl.CloseAll--> false</color>
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/ui/CViewCtrl"]:280: in function 'CloseAll'
	[string "logic/war/CWarCtrl"]:670: in function 'SwitchEnv'
	[string "logic/war/CWarCtrl"]:587: in function 'Start'
	[string "net/netwar"]:51: in function <[string "net/netwar"]:5>
	[C]: in function 'xxpcall'
	[string "net/CNetCtrl"]:318: in function 'ProtoCall'
	[string "net/CNetCtrl"]:300: in function 'ProcessProto'
	[string "net/CNetCtrl"]:251: in function 'ProtoHandlerCheck'
	[string "net/CNetCtrl"]:262: in function 'Receive'
	[string "net/CNetCtrl"]:175: in function 'OnSocketEvent'
	[string "net/CNetCtrl"]:111: in function 'm_Callback'
	[string "net/CTcpClient"]:43: in function <[string "net/CTcpClient"]:37>
[core/global.lua:59]:<color=#ffeb04>CloseAll-->CloseView:  CChapterFuBenMainView</color>
[logic/ui/CViewCtrl.lua:104]:CChapterFuBenMainView     CloseView
[logic/ui/CViewCtrl.lua:94]:CWarFloatView ShowView
[logic/ui/CViewCtrl.lua:94]:CWarMainView ShowView
[core/global.lua:59]:<color=#ffeb04>=00000=  userdata: 0x7f2ae5f0 nil</color>
[core/table.lua:94]:<--Net Send: war.C2GSWarSetPlaySpeed = {
|  play_speed = 66
|  war_id = 66
}
[core/global.lua:59]:<color=#ffeb04>读取地图： 5020</color>
[core/global.lua:59]:<color=#ffeb04>Unity2017.4.30f1 localScale</color>
[logic/ui/CViewBase.lua:125]:CWarFloatView LoadDone!
[logic/ui/CViewBase.lua:125]:CWarMainView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/global.lua:59]:<color=#ffeb04>地图加载完成: 5020 ,当前地图: 1020</color>
[core/global.lua:59]:<color=#ffeb04>删除地图: 1020</color>
[logic/base/CResCtrl.lua:599]:res gc step start!
[core/table.lua:94]:-->Net Receive: war.GS2CEnterWar = {}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWave = {
|  cur_wave = 1
|  sum_wave = 1
}
[core/global.lua:59]:<color=#ffeb04>--->GS2CWarWave: 1 1</color>
[core/table.lua:94]:-->Net Receive: war.GS2CWarAddWarrior = {
|  camp_id = 1
|  type = 1
|  war_id = 66
|  warrior = {
|  |  pflist = {
|  |  |  [1] = {
|  |  |  |  id = 3004
|  |  |  |  level = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  id = 3001
|  |  |  |  level = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  id = 3002
|  |  |  |  level = 1
|  |  |  }
|  |  }
|  |  pid = 10008
|  |  pos = 1
|  |  status = {
|  |  |  auto_skill = 3001
|  |  |  hp = 2928
|  |  |  mask = "7e"
|  |  |  max_hp = 2928
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 120
|  |  |  |  weapon = 2500
|  |  |  }
|  |  |  name = "颔首之孵化者"
|  |  |  status = 1
|  |  }
|  |  wid = 1
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 3001
|  hp = 2928
|  max_hp = 2928
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 120
|  |  weapon = 2500
|  }
|  name = "颔首之孵化者"
|  status = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarAddWarrior = {
|  camp_id = 1
|  partnerwarrior = {
|  |  owner = 1
|  |  parid = 1
|  |  pflist = {
|  |  |  [1] = {
|  |  |  |  id = 30201
|  |  |  |  level = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  id = 30202
|  |  |  |  level = 1
|  |  |  }
|  |  }
|  |  pos = 5
|  |  status = {
|  |  |  auto_skill = 30202
|  |  |  hp = 3120
|  |  |  mask = "7e"
|  |  |  max_hp = 3120
|  |  |  model_info = {
|  |  |  |  shape = 302
|  |  |  |  skin = 203020
|  |  |  }
|  |  |  name = "重华"
|  |  |  status = 1
|  |  }
|  |  wid = 2
|  }
|  type = 4
|  war_id = 66
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 30202
|  hp = 3120
|  max_hp = 3120
|  model_info = {
|  |  shape = 302
|  |  skin = 203020
|  }
|  name = "重华"
|  status = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarAddWarrior = {
|  camp_id = 1
|  partnerwarrior = {
|  |  owner = 1
|  |  parid = 2
|  |  pflist = {
|  |  |  [1] = {
|  |  |  |  id = 50201
|  |  |  |  level = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  id = 50202
|  |  |  |  level = 1
|  |  |  }
|  |  }
|  |  pos = 2
|  |  status = {
|  |  |  auto_skill = 50201
|  |  |  hp = 2611
|  |  |  mask = "7e"
|  |  |  max_hp = 2611
|  |  |  model_info = {
|  |  |  |  shape = 502
|  |  |  |  skin = 205020
|  |  |  }
|  |  |  name = "马面面"
|  |  |  status = 1
|  |  }
|  |  wid = 3
|  }
|  type = 4
|  war_id = 66
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 50201
|  hp = 2611
|  max_hp = 2611
|  model_info = {
|  |  shape = 502
|  |  skin = 205020
|  }
|  name = "马面面"
|  status = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarAddWarrior = {
|  camp_id = 1
|  partnerwarrior = {
|  |  owner = 1
|  |  parid = 3
|  |  pflist = {
|  |  |  [1] = {
|  |  |  |  id = 40301
|  |  |  |  level = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  id = 40302
|  |  |  |  level = 1
|  |  |  }
|  |  }
|  |  pos = 3
|  |  status = {
|  |  |  auto_skill = 40301
|  |  |  hp = 2441
|  |  |  mask = "7e"
|  |  |  max_hp = 2441
|  |  |  model_info = {
|  |  |  |  shape = 403
|  |  |  |  skin = 204030
|  |  |  }
|  |  |  name = "蛇姬"
|  |  |  status = 1
|  |  }
|  |  wid = 4
|  }
|  type = 4
|  war_id = 66
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 40301
|  hp = 2441
|  max_hp = 2441
|  model_info = {
|  |  shape = 403
|  |  skin = 204030
|  }
|  name = "蛇姬"
|  status = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarAddWarrior = {
|  camp_id = 2
|  npcwarrior = {
|  |  pos = 1
|  |  show_lv = 10
|  |  special_skill = {
|  |  |  cur_grid = 3
|  |  |  skill_id = 41802
|  |  |  sum_grid = 3
|  |  }
|  |  status = {
|  |  |  hp = 900
|  |  |  mask = "7e"
|  |  |  max_hp = 900
|  |  |  model_info = {
|  |  |  |  shape = 418
|  |  |  |  weapon = 2100
|  |  |  }
|  |  |  name = "嘟嘟噜"
|  |  |  status = 1
|  |  }
|  |  wid = 5
|  }
|  type = 2
|  war_id = 66
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 0
|  hp = 900
|  max_hp = 900
|  model_info = {
|  |  shape = 418
|  |  weapon = 2100
|  }
|  name = "嘟嘟噜"
|  status = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarAddWarrior = {
|  camp_id = 2
|  npcwarrior = {
|  |  pos = 2
|  |  show_lv = 10
|  |  special_skill = {
|  |  |  cur_grid = 2
|  |  |  skill_id = 101102
|  |  |  sum_grid = 3
|  |  }
|  |  status = {
|  |  |  hp = 600
|  |  |  mask = "7e"
|  |  |  max_hp = 600
|  |  |  model_info = {
|  |  |  |  shape = 1011
|  |  |  |  weapon = 2100
|  |  |  }
|  |  |  name = "遥"
|  |  |  status = 1
|  |  }
|  |  wid = 6
|  }
|  type = 2
|  war_id = 66
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 0
|  hp = 600
|  max_hp = 600
|  model_info = {
|  |  shape = 1011
|  |  weapon = 2100
|  }
|  name = "遥"
|  status = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarAddWarrior = {
|  camp_id = 2
|  npcwarrior = {
|  |  pos = 3
|  |  show_lv = 10
|  |  special_skill = {
|  |  |  cur_grid = 2
|  |  |  skill_id = 101002
|  |  |  sum_grid = 3
|  |  }
|  |  status = {
|  |  |  hp = 600
|  |  |  mask = "7e"
|  |  |  max_hp = 600
|  |  |  model_info = {
|  |  |  |  shape = 1010
|  |  |  |  weapon = 2100
|  |  |  }
|  |  |  name = "丽丝"
|  |  |  status = 1
|  |  }
|  |  wid = 7
|  }
|  type = 2
|  war_id = 66
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 0
|  hp = 600
|  max_hp = 600
|  model_info = {
|  |  shape = 1010
|  |  weapon = 2100
|  }
|  name = "丽丝"
|  status = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSpeed = {
|  speed_list = {
|  |  [1] = {
|  |  |  speed = 315
|  |  |  wid = 3
|  |  }
|  |  [2] = {
|  |  |  speed = 420
|  |  |  wid = 2
|  |  }
|  |  [3] = {
|  |  |  speed = 613
|  |  |  wid = 6
|  |  }
|  |  [4] = {
|  |  |  speed = 649
|  |  |  wid = 5
|  |  }
|  |  [5] = {
|  |  |  speed = 735
|  |  |  wid = 4
|  |  }
|  |  [6] = {
|  |  |  speed = 753
|  |  |  wid = 1
|  |  }
|  |  [7] = {
|  |  |  speed = 830
|  |  |  wid = 7
|  |  }
|  }
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 80
|  camp_id = 1
|  sp = 80
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarBoutStart = {
|  bout_id = 1
|  war_id = 66
}
[core/global.lua:59]:<color=#ffeb04>--->GS2CWarBoutStart: 1</color>
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  |  [2] = {
|  |  |  skill = 30201
|  |  |  wid = 2
|  |  }
|  |  [3] = {
|  |  |  skill = 50201
|  |  |  wid = 3
|  |  }
|  |  [4] = {
|  |  |  skill = 40301
|  |  |  wid = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSpeed = {
|  speed_list = {
|  |  [1] = {
|  |  |  camp = 2
|  |  |  speed = 830
|  |  |  wid = 7
|  |  }
|  |  [2] = {
|  |  |  camp = 1
|  |  |  speed = 753
|  |  |  wid = 1
|  |  }
|  |  [3] = {
|  |  |  camp = 1
|  |  |  speed = 735
|  |  |  wid = 4
|  |  }
|  |  [4] = {
|  |  |  camp = 2
|  |  |  speed = 649
|  |  |  wid = 5
|  |  }
|  |  [5] = {
|  |  |  camp = 2
|  |  |  speed = 613
|  |  |  wid = 6
|  |  }
|  |  [6] = {
|  |  |  camp = 1
|  |  |  speed = 420
|  |  |  wid = 2
|  |  }
|  |  [7] = {
|  |  |  camp = 1
|  |  |  speed = 315
|  |  |  wid = 3
|  |  }
|  }
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionStart = {
|  action_id = 1
|  war_id = 66
|  wid = 7
}
[core/global.lua:59]:<color=#ffeb04>--->Proto SectionStart: 1</color>
[core/table.lua:94]:-->Net Receive: war.GS2CWarAction = {
|  war_id = 66
|  wid = 7
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSkill = {
|  action_wlist = {
|  |  [1] = 7
|  }
|  magic_id = 1
|  select_wlist = {
|  |  [1] = 2
|  }
|  skill_id = 101001
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 2
|  camp_id = 1
|  sp = 81
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 2
|  camp_id = 1
|  sp = 81
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 2
|  camp_id = 1
|  sp = 81
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 2
|  camp_id = 1
|  sp = 81
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 3051
|  |  mask = "2"
|  }
|  type = 4
|  war_id = 66
|  wid = 2
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 3051
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -69
|  war_id = 66
|  wid = 2
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarGoback = {
|  action_wid = 7
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSpeed = {
|  speed_list = {
|  |  [1] = {
|  |  |  camp = 1
|  |  |  speed = 753
|  |  |  wid = 1
|  |  }
|  |  [2] = {
|  |  |  camp = 1
|  |  |  speed = 735
|  |  |  wid = 4
|  |  }
|  |  [3] = {
|  |  |  camp = 2
|  |  |  speed = 649
|  |  |  wid = 5
|  |  }
|  |  [4] = {
|  |  |  camp = 2
|  |  |  speed = 613
|  |  |  wid = 6
|  |  }
|  |  [5] = {
|  |  |  camp = 1
|  |  |  speed = 420
|  |  |  wid = 2
|  |  }
|  |  [6] = {
|  |  |  camp = 1
|  |  |  speed = 315
|  |  |  wid = 3
|  |  }
|  |  [7] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 830
|  |  |  wid = 7
|  |  }
|  }
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionEnd = {
|  action_id = 1
|  war_id = 66
|  wid = 7
}
[logic/ui/CViewCtrl.lua:94]:CMainMenuView ShowView
[core/global.lua:59]:<color=#ffeb04>SectionEnd !! 1 0 0 1 0 1 flag: 1</color>
[core/table.lua:94]:-->Net Receive: war.GS2CWarChapterInfo = {
|  start_time = 1753682629
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSetPlaySpeed = {
|  play_speed = 66
|  war_id = 66
}
[logic/base/CResCtrl.lua:626]:res gc finish!
[logic/ui/CSprite.lua:159]:DynamicSprite dSprInfo    Item    10012
[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
[core/global.lua:59]:<color=#ffeb04>UpdateNewWaveTag:true</color>
[logic/war/CWarCtrl.lua:1404]:自定义站位    1    2.5499999523163    -2.4900000095367
[logic/war/CWarCtrl.lua:1404]:自定义站位    5    1.2699999809265    -1.1900000572205
[logic/war/CWarCtrl.lua:1404]:自定义站位    2    0.41999998688698    -4.460000038147
[logic/war/CWarCtrl.lua:1404]:自定义站位    3    4.5599999427795    -0.44999998807907
[logic/war/CWarCtrl.lua:1404]:自定义站位    1    -2.3599998950958    2.3900001049042
[logic/model/CModel.lua:181]:造型:    418    ,没有武器    2100
[logic/war/CWarCtrl.lua:1404]:自定义站位    2    -4.3099999427795    0.31000000238419
[logic/war/CWarCtrl.lua:1404]:自定义站位    3    -0.18999999761581    4.3200001716614
[logic/model/CModel.lua:181]:造型:    1010    ,没有武器    2100
[logic/model/CModel.lua:181]:造型:    1011    ,没有武器    2100
[logic/ui/CViewCtrl.lua:104]:CWarBossView     CloseView
[core/global.lua:59]:<color=#ffeb04>UpdateNewWaveTag:false</color>
[core/global.lua:59]:<color=#ffeb04>SectionEnd.process 1 1</color>
[core/global.lua:59]:<color=#ffeb04>SectionStart  1 1 1 flag: 1</color>
[logic/war/CWarCmd.lua:510]:CWarCmd.Magic    101001_1_1
[logic/war/CWarCmd.lua:602]:增加归位指令    101001_1_1    true    4393
[logic/magic/CMagicUnit.lua:57]:法术开始:    101001    1    1
[core/global.lua:59]:<color=#ffeb04>客户端主动技能喊招： 101001</color>
[core/table.lua:94]:<--Net Send: war.C2GSNextActionEnd = {
|  action_id = 1
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 91
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 91
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 91
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 91
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionStart = {
|  action_id = 2
|  left_time = 4
|  war_id = 66
|  wid = 1
}
[core/global.lua:59]:<color=#ffeb04>--->Proto SectionStart: 2</color>
[core/global.lua:59]:<color=#ffeb04>SectionStart  1 1 2 flag: 0</color>
[core/global.lua:59]:<color=#ffeb04>TimeUp!!!! Section:2</color>
[core/table.lua:94]:<--Net Send: war.C2GSWarAutoFight = {
|  type = 2
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  auto_skill = 3001
|  |  mask = "40"
|  }
|  type = 1
|  war_id = 66
|  wid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 3001
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  auto_skill = 30202
|  |  mask = "40"
|  }
|  type = 4
|  war_id = 66
|  wid = 2
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 30202
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  auto_skill = 50201
|  |  mask = "40"
|  }
|  type = 4
|  war_id = 66
|  wid = 3
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 50201
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  auto_skill = 40301
|  |  mask = "40"
|  }
|  type = 4
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 40301
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarCommand = {
|  war_id = 66
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarAction = {
|  war_id = 66
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSkill = {
|  action_wlist = {
|  |  [1] = 1
|  }
|  magic_id = 1
|  select_wlist = {
|  |  [1] = 7
|  }
|  skill_id = 3001
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 496
|  |  mask = "2"
|  }
|  type = 2
|  war_id = 66
|  wid = 7
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 496
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -104
|  war_id = 66
|  wid = 7
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 389
|  |  mask = "2"
|  }
|  type = 2
|  war_id = 66
|  wid = 7
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 389
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -107
|  war_id = 66
|  wid = 7
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarGoback = {
|  action_wid = 1
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSpeed = {
|  speed_list = {
|  |  [1] = {
|  |  |  camp = 1
|  |  |  speed = 735
|  |  |  wid = 4
|  |  }
|  |  [2] = {
|  |  |  camp = 2
|  |  |  speed = 649
|  |  |  wid = 5
|  |  }
|  |  [3] = {
|  |  |  camp = 2
|  |  |  speed = 613
|  |  |  wid = 6
|  |  }
|  |  [4] = {
|  |  |  camp = 1
|  |  |  speed = 420
|  |  |  wid = 2
|  |  }
|  |  [5] = {
|  |  |  camp = 1
|  |  |  speed = 315
|  |  |  wid = 3
|  |  }
|  |  [6] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 830
|  |  |  wid = 7
|  |  }
|  |  [7] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 753
|  |  |  wid = 1
|  |  }
|  }
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionEnd = {
|  action_id = 2
|  war_id = 66
|  wid = 1
}
[core/global.lua:59]:<color=#ffeb04>SectionEnd !! 2 2 1 1 1 1 flag: 1</color>
[core/global.lua:59]:<color=#ffeb04>SectionEnd.process 1 1</color>
[logic/war/CWarCmd.lua:510]:CWarCmd.Magic    3001_1_2
[logic/war/CWarCmd.lua:602]:增加归位指令    3001_1_2    true    4706
[logic/magic/CMagicUnit.lua:57]:法术开始:    3001    1    2
[core/global.lua:59]:<color=#ffeb04>客户端主动技能喊招： 3001</color>
[core/table.lua:94]:<--Net Send: war.C2GSNextActionEnd = {
|  action_id = 2
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionStart = {
|  action_id = 3
|  war_id = 66
|  wid = 4
}
[core/global.lua:59]:<color=#ffeb04>--->Proto SectionStart: 3</color>
[core/table.lua:94]:-->Net Receive: war.GS2CWarCommand = {
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarAction = {
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSkill = {
|  action_wlist = {
|  |  [1] = 4
|  }
|  magic_id = 1
|  select_wlist = {
|  |  [1] = 6
|  }
|  skill_id = 40301
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 356
|  |  mask = "2"
|  }
|  type = 2
|  war_id = 66
|  wid = 6
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 356
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -244
|  war_id = 66
|  wid = 6
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarGoback = {
|  action_wid = 4
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSpeed = {
|  speed_list = {
|  |  [1] = {
|  |  |  camp = 2
|  |  |  speed = 649
|  |  |  wid = 5
|  |  }
|  |  [2] = {
|  |  |  camp = 2
|  |  |  speed = 613
|  |  |  wid = 6
|  |  }
|  |  [3] = {
|  |  |  camp = 1
|  |  |  speed = 420
|  |  |  wid = 2
|  |  }
|  |  [4] = {
|  |  |  camp = 1
|  |  |  speed = 315
|  |  |  wid = 3
|  |  }
|  |  [5] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 830
|  |  |  wid = 7
|  |  }
|  |  [6] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 753
|  |  |  wid = 1
|  |  }
|  |  [7] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 735
|  |  |  wid = 4
|  |  }
|  }
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionEnd = {
|  action_id = 3
|  war_id = 66
|  wid = 4
}
[core/global.lua:59]:<color=#ffeb04>SectionEnd !! 3 2 1 1 1 1 flag: 2</color>
[core/global.lua:59]:<color=#ffeb04>SectionEnd.process 1 1</color>
[core/global.lua:59]:<color=#ffeb04>SectionStart  1 1 3 flag: 1</color>
[logic/war/CWarCmd.lua:510]:CWarCmd.Magic    40301_1_3
[logic/war/CWarCmd.lua:602]:增加归位指令    40301_1_3    true    4766
[logic/magic/CMagicUnit.lua:57]:法术开始:    40301    1    3
[core/global.lua:59]:<color=#ffeb04>客户端主动技能喊招： 40301</color>
[core/table.lua:94]:<--Net Send: war.C2GSNextActionEnd = {
|  action_id = 3
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionStart = {
|  action_id = 4
|  war_id = 66
|  wid = 5
}
[core/global.lua:59]:<color=#ffeb04>--->Proto SectionStart: 4</color>
[core/table.lua:94]:-->Net Receive: war.GS2CWarAction = {
|  war_id = 66
|  wid = 5
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSkill = {
|  action_wlist = {
|  |  [1] = 5
|  }
|  magic_id = 1
|  select_wlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  }
|  skill_id = 41802
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 1
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 1
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 1
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 1
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 2815
|  |  mask = "2"
|  }
|  type = 1
|  war_id = 66
|  wid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 2815
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -113
|  war_id = 66
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 1
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 1
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 1
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 1
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 2711
|  |  mask = "2"
|  }
|  type = 1
|  war_id = 66
|  wid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 2711
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -104
|  war_id = 66
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 1
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 1
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 1
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 1
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 2608
|  |  mask = "2"
|  }
|  type = 1
|  war_id = 66
|  wid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 2608
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -103
|  war_id = 66
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 2
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 2
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 2
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 2
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 2965
|  |  mask = "2"
|  }
|  type = 4
|  war_id = 66
|  wid = 2
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 2965
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -86
|  war_id = 66
|  wid = 2
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 2
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 2
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 2
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 2
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 2872
|  |  mask = "2"
|  }
|  type = 4
|  war_id = 66
|  wid = 2
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 2872
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -93
|  war_id = 66
|  wid = 2
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 2
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 2
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 2
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 2
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 2791
|  |  mask = "2"
|  }
|  type = 4
|  war_id = 66
|  wid = 2
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 2791
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -81
|  war_id = 66
|  wid = 2
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 3
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 3
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 3
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 3
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 2502
|  |  mask = "2"
|  }
|  type = 4
|  war_id = 66
|  wid = 3
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 2502
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -109
|  war_id = 66
|  wid = 3
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 3
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 3
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 3
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 3
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 2392
|  |  mask = "2"
|  }
|  type = 4
|  war_id = 66
|  wid = 3
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 2392
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -110
|  war_id = 66
|  wid = 3
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 3
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 3
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 3
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 3
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 2232
|  |  mask = "2"
|  }
|  type = 4
|  war_id = 66
|  wid = 3
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 2232
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -160
|  iscrit = 1
|  war_id = 66
|  wid = 3
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 4
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 4
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 4
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 4
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 2341
|  |  mask = "2"
|  }
|  type = 4
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 2341
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -100
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 4
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 4
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 4
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 4
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 2239
|  |  mask = "2"
|  }
|  type = 4
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 2239
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -102
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 4
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 4
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 4
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 4
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 2124
|  |  mask = "2"
|  }
|  type = 4
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 2124
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -115
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarGoback = {
|  action_wid = 5
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSpeed = {
|  speed_list = {
|  |  [1] = {
|  |  |  camp = 2
|  |  |  speed = 613
|  |  |  wid = 6
|  |  }
|  |  [2] = {
|  |  |  camp = 1
|  |  |  speed = 420
|  |  |  wid = 2
|  |  }
|  |  [3] = {
|  |  |  camp = 1
|  |  |  speed = 315
|  |  |  wid = 3
|  |  }
|  |  [4] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 830
|  |  |  wid = 7
|  |  }
|  |  [5] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 753
|  |  |  wid = 1
|  |  }
|  |  [6] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 735
|  |  |  wid = 4
|  |  }
|  |  [7] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 649
|  |  |  wid = 5
|  |  }
|  }
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionEnd = {
|  action_id = 4
|  war_id = 66
|  wid = 5
}
[core/global.lua:59]:<color=#ffeb04>SectionEnd !! 4 3 1 1 1 1 flag: 2</color>
[core/global.lua:59]:<color=#ffeb04>SectionEnd.process 1 1</color>
[core/global.lua:59]:<color=#ffeb04>SectionStart  1 1 4 flag: 1</color>
[logic/war/CWarCmd.lua:510]:CWarCmd.Magic    41802_1_4
[logic/war/CWarCmd.lua:602]:增加归位指令    41802_1_4    true    4871
[logic/magic/CMagicUnit.lua:57]:法术开始:    41802    1    4
[core/global.lua:59]:<color=#ffeb04>客户端主动技能喊招： 41802</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682637
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:03:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: war.C2GSNextActionEnd = {
|  action_id = 4
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionStart = {
|  action_id = 5
|  war_id = 66
|  wid = 6
}
[core/global.lua:59]:<color=#ffeb04>--->Proto SectionStart: 5</color>
[core/table.lua:94]:-->Net Receive: war.GS2CWarAction = {
|  war_id = 66
|  wid = 6
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSkill = {
|  action_wlist = {
|  |  [1] = 6
|  }
|  magic_id = 1
|  select_wlist = {
|  |  [1] = 4
|  }
|  skill_id = 101101
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 4
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 4
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 4
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 1
|  attack = 4
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 2021
|  |  mask = "2"
|  }
|  type = 4
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 2021
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -103
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarGoback = {
|  action_wid = 6
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSpeed = {
|  speed_list = {
|  |  [1] = {
|  |  |  camp = 1
|  |  |  speed = 420
|  |  |  wid = 2
|  |  }
|  |  [2] = {
|  |  |  camp = 1
|  |  |  speed = 315
|  |  |  wid = 3
|  |  }
|  |  [3] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 830
|  |  |  wid = 7
|  |  }
|  |  [4] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 753
|  |  |  wid = 1
|  |  }
|  |  [5] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 735
|  |  |  wid = 4
|  |  }
|  |  [6] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 649
|  |  |  wid = 5
|  |  }
|  |  [7] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 613
|  |  |  wid = 6
|  |  }
|  }
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionEnd = {
|  action_id = 5
|  war_id = 66
|  wid = 6
}
[core/global.lua:59]:<color=#ffeb04>SectionEnd !! 5 4 1 1 1 1 flag: 2</color>
[core/global.lua:59]:<color=#ffeb04>SectionEnd.process 1 1</color>
[core/global.lua:59]:<color=#ffeb04>SectionStart  1 1 5 flag: 1</color>
[logic/war/CWarCmd.lua:510]:CWarCmd.Magic    101101_1_5
[logic/war/CWarCmd.lua:602]:增加归位指令    101101_1_5    true    5021
[logic/magic/CMagicUnit.lua:57]:法术开始:    101101    1    5
[core/global.lua:59]:<color=#ffeb04>客户端主动技能喊招： 101101</color>
[core/table.lua:94]:<--Net Send: war.C2GSNextActionEnd = {
|  action_id = 5
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:<--Net Send: war.C2GSWarAutoFight = {
|  type = 0
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 100
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionStart = {
|  action_id = 6
|  war_id = 66
|  wid = 2
}
[core/global.lua:59]:<color=#ffeb04>--->Proto SectionStart: 6</color>
[core/table.lua:94]:-->Net Receive: war.GS2CWarCommand = {
|  war_id = 66
|  wid = 2
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = -40
|  camp_id = 1
|  sp = 60
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = -40
|  camp_id = 1
|  sp = 60
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = -40
|  camp_id = 1
|  sp = 60
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = -40
|  camp_id = 1
|  sp = 60
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarAction = {
|  war_id = 66
|  wid = 2
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSkill = {
|  action_wlist = {
|  |  [1] = 2
|  }
|  magic_id = 1
|  select_wlist = {
|  |  [1] = 7
|  |  [2] = 5
|  |  [3] = 6
|  }
|  skill_id = 30202
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 168
|  |  mask = "2"
|  }
|  type = 2
|  war_id = 66
|  wid = 7
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 168
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -221
|  war_id = 66
|  wid = 7
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 624
|  |  mask = "2"
|  }
|  type = 2
|  war_id = 66
|  wid = 5
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 624
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -276
|  war_id = 66
|  wid = 5
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 72
|  |  mask = "2"
|  }
|  type = 2
|  war_id = 66
|  wid = 6
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 72
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -284
|  war_id = 66
|  wid = 6
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarGoback = {
|  action_wid = 2
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSpeed = {
|  speed_list = {
|  |  [1] = {
|  |  |  camp = 1
|  |  |  speed = 315
|  |  |  wid = 3
|  |  }
|  |  [2] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 830
|  |  |  wid = 7
|  |  }
|  |  [3] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 753
|  |  |  wid = 1
|  |  }
|  |  [4] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 735
|  |  |  wid = 4
|  |  }
|  |  [5] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 649
|  |  |  wid = 5
|  |  }
|  |  [6] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 613
|  |  |  wid = 6
|  |  }
|  |  [7] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 420
|  |  |  wid = 2
|  |  }
|  }
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionEnd = {
|  action_id = 6
|  war_id = 66
|  wid = 2
}
[core/global.lua:59]:<color=#ffeb04>SectionEnd !! 6 5 1 1 1 1 flag: 2</color>
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  mask = "40"
|  }
|  type = 1
|  war_id = 66
|  wid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 0
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  mask = "40"
|  }
|  type = 4
|  war_id = 66
|  wid = 2
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 0
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  mask = "40"
|  }
|  type = 4
|  war_id = 66
|  wid = 3
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 0
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  mask = "40"
|  }
|  type = 4
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 0
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarTarget = {
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarTarget = {
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarTarget = {
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarTarget = {
|  war_id = 66
}
[core/global.lua:59]:<color=#ffeb04>SectionEnd.process 1 1</color>
[core/global.lua:59]:<color=#ffeb04>SectionStart  1 1 6 flag: 1</color>
[logic/war/CWarCmd.lua:510]:CWarCmd.Magic    30202_1_6
[logic/war/CWarCmd.lua:602]:增加归位指令    30202_1_6    true    5092
[logic/magic/CMagicUnit.lua:57]:法术开始:    30202    1    6
[core/global.lua:59]:<color=#ffeb04>客户端主动技能喊招： 30202</color>
[logic/war/CWarrior.lua:1023]:浮空中播放受击
[logic/war/CWarrior.lua:1023]:浮空中播放受击
[logic/war/CWarrior.lua:1023]:浮空中播放受击
[logic/magic/CMagicUnit.lua:57]:法术开始:    99    302    7
[core/table.lua:94]:<--Net Send: war.C2GSNextActionEnd = {
|  action_id = 6
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 70
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 70
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 70
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 70
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionStart = {
|  action_id = 7
|  left_time = 15
|  war_id = 66
|  wid = 3
}
[core/global.lua:59]:<color=#ffeb04>--->Proto SectionStart: 7</color>
[core/global.lua:59]:<color=#ffeb04>SectionStart  1 1 7 flag: 0</color>
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 50201
|  war_id = 66
|  wid = 3
}
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  |  [2] = {
|  |  |  skill = 30201
|  |  |  wid = 2
|  |  }
|  |  [3] = {
|  |  |  skill = 40301
|  |  |  wid = 4
|  |  }
|  |  [4] = {
|  |  |  skill = 50201
|  |  |  wid = 3
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 50201
|  war_id = 66
|  wid = 3
}
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  |  [2] = {
|  |  |  skill = 30201
|  |  |  wid = 2
|  |  }
|  |  [3] = {
|  |  |  skill = 40301
|  |  |  wid = 4
|  |  }
|  |  [4] = {
|  |  |  skill = 50201
|  |  |  wid = 3
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 50202
|  war_id = 66
|  wid = 3
}
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  |  [2] = {
|  |  |  skill = 30201
|  |  |  wid = 2
|  |  }
|  |  [3] = {
|  |  |  skill = 40301
|  |  |  wid = 4
|  |  }
|  |  [4] = {
|  |  |  skill = 50202
|  |  |  wid = 3
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682647
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:04:07
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/war/CWarOrderCtrl.lua:415]:MagicSend    50202
[core/table.lua:94]:<--Net Send: war.C2GSWarSkill = {
|  action_wlist = {
|  |  [1] = 3
|  }
|  select_wlist = {
|  |  [1] = 5
|  }
|  skill_id = 50202
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarCommand = {
|  war_id = 66
|  wid = 3
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = -40
|  camp_id = 1
|  sp = 30
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = -40
|  camp_id = 1
|  sp = 30
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = -40
|  camp_id = 1
|  sp = 30
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = -40
|  camp_id = 1
|  sp = 30
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarAction = {
|  war_id = 66
|  wid = 3
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSkill = {
|  action_wlist = {
|  |  [1] = 3
|  }
|  magic_id = 1
|  select_wlist = {
|  |  [1] = 5
|  |  [2] = 6
|  |  [3] = 7
|  }
|  skill_id = 50202
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 388
|  |  mask = "2"
|  }
|  type = 2
|  war_id = 66
|  wid = 5
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 388
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -236
|  war_id = 66
|  wid = 5
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  mask = "2"
|  }
|  type = 2
|  war_id = 66
|  wid = 6
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 0
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  mask = "20"
|  |  status = 2
|  }
|  type = 2
|  war_id = 66
|  wid = 6
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  status = 2
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -230
|  war_id = 66
|  wid = 6
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  mask = "2"
|  }
|  type = 2
|  war_id = 66
|  wid = 7
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 0
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  mask = "20"
|  |  status = 2
|  }
|  type = 2
|  war_id = 66
|  wid = 7
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  status = 2
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -376
|  iscrit = 1
|  war_id = 66
|  wid = 7
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarGoback = {
|  action_wid = 3
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CShowWarSkill = {
|  skill = 50203
|  type = 1
|  wid = 3
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarBuffBout = {
|  bout = 255
|  buff_id = 1013
|  level = 1
|  produce_wid = 3
|  war_id = 66
|  wid = 3
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSpeed = {
|  speed_list = {
|  |  [1] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 830
|  |  |  wid = 7
|  |  }
|  |  [2] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 753
|  |  |  wid = 1
|  |  }
|  |  [3] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 735
|  |  |  wid = 4
|  |  }
|  |  [4] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 649
|  |  |  wid = 5
|  |  }
|  |  [5] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 613
|  |  |  wid = 6
|  |  }
|  |  [6] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 420
|  |  |  wid = 2
|  |  }
|  |  [7] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 315
|  |  |  wid = 3
|  |  }
|  }
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionEnd = {
|  action_id = 7
|  war_id = 66
|  wid = 3
}
[core/global.lua:59]:<color=#ffeb04>SectionEnd !! 7 7 1 1 1 1 flag: 1</color>
[core/global.lua:59]:<color=#ffeb04>SectionEnd.process 1 1</color>
[logic/war/CWarCmd.lua:510]:CWarCmd.Magic    50202_1_8
[logic/war/CWarCmd.lua:602]:增加归位指令    50202_1_8    true    5232
[logic/magic/CMagicUnit.lua:57]:法术开始:    50202    1    8
[core/global.lua:59]:<color=#ffeb04>客户端主动技能喊招： 50202</color>
[core/global.lua:59]:<color=#ffeb04>已死亡目标Hit 丽丝</color>
[core/global.lua:59]:<color=#ffeb04>已死亡目标Hit 遥</color>
[core/global.lua:59]:<color=#ffeb04>服务器被动技能图标： 50203</color>
[core/table.lua:94]:<--Net Send: war.C2GSNextActionEnd = {
|  action_id = 7
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarBoutEnd = {
|  bout_id = 1
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarBoutStart = {
|  bout_id = 2
|  war_id = 66
}
[core/global.lua:59]:<color=#ffeb04>--->GS2CWarBoutStart: 2</color>
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  |  [2] = {
|  |  |  skill = 30201
|  |  |  wid = 2
|  |  }
|  |  [3] = {
|  |  |  skill = 50201
|  |  |  wid = 3
|  |  }
|  |  [4] = {
|  |  |  skill = 40301
|  |  |  wid = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSpeed = {
|  speed_list = {
|  |  [1] = {
|  |  |  camp = 2
|  |  |  speed = 830
|  |  |  wid = 7
|  |  }
|  |  [2] = {
|  |  |  camp = 1
|  |  |  speed = 753
|  |  |  wid = 1
|  |  }
|  |  [3] = {
|  |  |  camp = 1
|  |  |  speed = 735
|  |  |  wid = 4
|  |  }
|  |  [4] = {
|  |  |  camp = 2
|  |  |  speed = 649
|  |  |  wid = 5
|  |  }
|  |  [5] = {
|  |  |  camp = 2
|  |  |  speed = 613
|  |  |  wid = 6
|  |  }
|  |  [6] = {
|  |  |  camp = 1
|  |  |  speed = 420
|  |  |  wid = 2
|  |  }
|  |  [7] = {
|  |  |  camp = 1
|  |  |  speed = 315
|  |  |  wid = 3
|  |  }
|  }
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionStart = {
|  action_id = 8
|  war_id = 66
|  wid = 7
}
[core/global.lua:59]:<color=#ffeb04>--->Proto SectionStart: 8</color>
[core/table.lua:94]:-->Net Receive: war.GS2CWarAction = {
|  war_id = 66
|  wid = 7
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSpeed = {
|  speed_list = {
|  |  [1] = {
|  |  |  camp = 1
|  |  |  speed = 753
|  |  |  wid = 1
|  |  }
|  |  [2] = {
|  |  |  camp = 1
|  |  |  speed = 735
|  |  |  wid = 4
|  |  }
|  |  [3] = {
|  |  |  camp = 2
|  |  |  speed = 649
|  |  |  wid = 5
|  |  }
|  |  [4] = {
|  |  |  camp = 2
|  |  |  speed = 613
|  |  |  wid = 6
|  |  }
|  |  [5] = {
|  |  |  camp = 1
|  |  |  speed = 420
|  |  |  wid = 2
|  |  }
|  |  [6] = {
|  |  |  camp = 1
|  |  |  speed = 315
|  |  |  wid = 3
|  |  }
|  |  [7] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 830
|  |  |  wid = 7
|  |  }
|  }
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionEnd = {
|  action_id = 8
|  war_id = 66
|  wid = 7
}
[core/global.lua:59]:<color=#ffeb04>SectionEnd !! 8 7 1 2 1 1 flag: 2</color>
[core/global.lua:59]:<color=#ffeb04>SectionEnd.process 2 1</color>
[core/global.lua:59]:<color=#ffeb04>SectionStart  1 2 8 flag: 1</color>
[core/table.lua:94]:<--Net Send: war.C2GSNextActionEnd = {
|  action_id = 8
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 40
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 40
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 40
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 40
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionStart = {
|  action_id = 9
|  left_time = 15
|  war_id = 66
|  wid = 1
}
[core/global.lua:59]:<color=#ffeb04>--->Proto SectionStart: 9</color>
[core/global.lua:59]:<color=#ffeb04>SectionStart  1 2 9 flag: 0</color>
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 3001
|  war_id = 66
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 30201
|  |  |  wid = 2
|  |  }
|  |  [2] = {
|  |  |  skill = 50201
|  |  |  wid = 3
|  |  }
|  |  [3] = {
|  |  |  skill = 40301
|  |  |  wid = 4
|  |  }
|  |  [4] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 3001
|  war_id = 66
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 30201
|  |  |  wid = 2
|  |  }
|  |  [2] = {
|  |  |  skill = 50201
|  |  |  wid = 3
|  |  }
|  |  [3] = {
|  |  |  skill = 40301
|  |  |  wid = 4
|  |  }
|  |  [4] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 3002
|  war_id = 66
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 30201
|  |  |  wid = 2
|  |  }
|  |  [2] = {
|  |  |  skill = 50201
|  |  |  wid = 3
|  |  }
|  |  [3] = {
|  |  |  skill = 40301
|  |  |  wid = 4
|  |  }
|  |  [4] = {
|  |  |  skill = 3002
|  |  |  wid = 1
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 3001
|  war_id = 66
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 30201
|  |  |  wid = 2
|  |  }
|  |  [2] = {
|  |  |  skill = 50201
|  |  |  wid = 3
|  |  }
|  |  [3] = {
|  |  |  skill = 40301
|  |  |  wid = 4
|  |  }
|  |  [4] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  }
}
[logic/war/CWarOrderCtrl.lua:415]:MagicSend    3001
[core/table.lua:94]:<--Net Send: war.C2GSWarSkill = {
|  action_wlist = {
|  |  [1] = 1
|  }
|  select_wlist = {
|  |  [1] = 5
|  }
|  skill_id = 3001
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarCommand = {
|  war_id = 66
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarAction = {
|  war_id = 66
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSkill = {
|  action_wlist = {
|  |  [1] = 1
|  }
|  magic_id = 1
|  select_wlist = {
|  |  [1] = 5
|  }
|  skill_id = 3001
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 208
|  |  mask = "2"
|  }
|  type = 2
|  war_id = 66
|  wid = 5
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 208
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -180
|  iscrit = 1
|  war_id = 66
|  wid = 5
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  hp = 115
|  |  mask = "2"
|  }
|  type = 2
|  war_id = 66
|  wid = 5
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 115
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -93
|  war_id = 66
|  wid = 5
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarGoback = {
|  action_wid = 1
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSpeed = {
|  speed_list = {
|  |  [1] = {
|  |  |  camp = 1
|  |  |  speed = 735
|  |  |  wid = 4
|  |  }
|  |  [2] = {
|  |  |  camp = 2
|  |  |  speed = 649
|  |  |  wid = 5
|  |  }
|  |  [3] = {
|  |  |  camp = 1
|  |  |  speed = 420
|  |  |  wid = 2
|  |  }
|  |  [4] = {
|  |  |  camp = 1
|  |  |  speed = 315
|  |  |  wid = 3
|  |  }
|  |  [5] = {
|  |  |  camp = 2
|  |  |  speed = 613
|  |  |  wid = 6
|  |  }
|  |  [6] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 830
|  |  |  wid = 7
|  |  }
|  |  [7] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 753
|  |  |  wid = 1
|  |  }
|  }
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionEnd = {
|  action_id = 9
|  war_id = 66
|  wid = 1
}
[core/global.lua:59]:<color=#ffeb04>SectionEnd !! 9 9 2 2 1 1 flag: 1</color>
[core/global.lua:59]:<color=#ffeb04>SectionEnd.process 2 1</color>
[logic/war/CWarCmd.lua:510]:CWarCmd.Magic    3001_1_9
[logic/war/CWarCmd.lua:602]:增加归位指令    3001_1_9    true    5382
[logic/magic/CMagicUnit.lua:57]:法术开始:    3001    1    9
[core/global.lua:59]:<color=#ffeb04>客户端主动技能喊招： 3001</color>
[core/table.lua:94]:<--Net Send: war.C2GSNextActionEnd = {
|  action_id = 9
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 50
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 50
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 50
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 50
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionStart = {
|  action_id = 10
|  left_time = 15
|  war_id = 66
|  wid = 4
}
[core/global.lua:59]:<color=#ffeb04>--->Proto SectionStart: 10</color>
[core/global.lua:59]:<color=#ffeb04>SectionStart  1 2 10 flag: 0</color>
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 40301
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 30201
|  |  |  wid = 2
|  |  }
|  |  [2] = {
|  |  |  skill = 50201
|  |  |  wid = 3
|  |  }
|  |  [3] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  |  [4] = {
|  |  |  skill = 40301
|  |  |  wid = 4
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 40301
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 30201
|  |  |  wid = 2
|  |  }
|  |  [2] = {
|  |  |  skill = 50201
|  |  |  wid = 3
|  |  }
|  |  [3] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  |  [4] = {
|  |  |  skill = 40301
|  |  |  wid = 4
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682657
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:04:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 40301
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 30201
|  |  |  wid = 2
|  |  }
|  |  [2] = {
|  |  |  skill = 50201
|  |  |  wid = 3
|  |  }
|  |  [3] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  |  [4] = {
|  |  |  skill = 40301
|  |  |  wid = 4
|  |  }
|  }
}
[logic/war/CWarOrderCtrl.lua:415]:MagicSend    40301
[core/table.lua:94]:<--Net Send: war.C2GSWarSkill = {
|  action_wlist = {
|  |  [1] = 4
|  }
|  select_wlist = {
|  |  [1] = 5
|  }
|  skill_id = 40301
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarCommand = {
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarAction = {
|  war_id = 66
|  wid = 4
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSkill = {
|  action_wlist = {
|  |  [1] = 4
|  }
|  magic_id = 1
|  select_wlist = {
|  |  [1] = 5
|  }
|  skill_id = 40301
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  mask = "2"
|  }
|  type = 2
|  war_id = 66
|  wid = 5
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  hp = 0
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  mask = "20"
|  |  status = 2
|  }
|  type = 2
|  war_id = 66
|  wid = 5
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  status = 2
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarDamage = {
|  damage = -200
|  war_id = 66
|  wid = 5
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarGoback = {
|  action_wid = 4
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSpeed = {
|  speed_list = {
|  |  [1] = {
|  |  |  camp = 2
|  |  |  speed = 649
|  |  |  wid = 5
|  |  }
|  |  [2] = {
|  |  |  camp = 1
|  |  |  speed = 420
|  |  |  wid = 2
|  |  }
|  |  [3] = {
|  |  |  camp = 1
|  |  |  speed = 315
|  |  |  wid = 3
|  |  }
|  |  [4] = {
|  |  |  camp = 2
|  |  |  speed = 613
|  |  |  wid = 6
|  |  }
|  |  [5] = {
|  |  |  action = 1
|  |  |  camp = 2
|  |  |  speed = 830
|  |  |  wid = 7
|  |  }
|  |  [6] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 753
|  |  |  wid = 1
|  |  }
|  |  [7] = {
|  |  |  action = 1
|  |  |  camp = 1
|  |  |  speed = 735
|  |  |  wid = 4
|  |  }
|  }
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionEnd = {
|  action_id = 10
|  war_id = 66
|  wid = 4
}
[core/global.lua:59]:<color=#ffeb04>SectionEnd !! 10 10 2 2 1 1 flag: 1</color>
[core/global.lua:59]:<color=#ffeb04>SectionEnd.process 2 1</color>
[logic/war/CWarCmd.lua:510]:CWarCmd.Magic    40301_1_10
[logic/war/CWarCmd.lua:602]:增加归位指令    40301_1_10    true    5474
[logic/magic/CMagicUnit.lua:57]:法术开始:    40301    1    10
[core/global.lua:59]:<color=#ffeb04>客户端主动技能喊招： 40301</color>
[core/global.lua:59]:<color=#ffeb04>已死亡目标Hit 嘟嘟噜</color>
[core/table.lua:94]:<--Net Send: war.C2GSNextActionEnd = {
|  action_id = 10
|  war_id = 66
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarResult = {
|  war_id = 66
|  win_side = 1
}
[core/global.lua:59]:<color=#ffeb04>CViewCtrl.CloseAll--> nil</color>
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/ui/CViewCtrl"]:280: in function 'CloseAll'
	[string "logic/war/CWarCmd"]:320: in function <[string "logic/war/CWarCmd"]:310>
	[C]: in function 'xxpcall'
	[string "logic/war/CWarCtrl"]:1507: in function 'UpdateCmds'
	[string "logic/war/CWarCtrl"]:1495: in function 'Update'
	[string "main"]:40: in function <[string "main"]:31>
[core/global.lua:59]:<color=#ffeb04>CloseAll-->CloseView:  CWarFloatView</color>
[logic/ui/CViewCtrl.lua:104]:CWarFloatView     CloseView
[core/global.lua:59]:<color=#ffeb04>CloseAll-->CloseView:  CWarMainView</color>
[logic/ui/CViewCtrl.lua:104]:CWarMainView     CloseView
[core/global.lua:59]:<color=#ffeb04>SimulateMagicCmd-> 97 2 false nil nil</color>
[logic/war/CWarCmd.lua:510]:CWarCmd.Magic    97_2_11
[logic/magic/CMagicUnit.lua:57]:法术开始:    97    2    11
[logic/magic/CMagicCmd.lua:165]:不删除特效    Effect/Magic/skybox_dark/Prefabs/skybox_dark.prefab
[core/table.lua:94]:-->Net Receive: war.GS2CWarDelWarrior = {
|  war_id = 66
|  wid = 1
}
[logic/magic/CMagicCmd.lua:165]:不删除特效    Effect/Magic/skybox_dark/Prefabs/skybox_deng.prefab
[logic/magic/CMagicCmd.lua:165]:不删除特效    Effect/Magic/skybox_dark/Prefabs/skybox_deng.prefab
[logic/magic/CMagicCmd.lua:165]:不删除特效    Effect/Magic/skybox_dark/Prefabs/skybox_deng.prefab
[logic/magic/CMagicCmd.lua:165]:不删除特效    Effect/Magic/skybox_dark/Prefabs/skybox_deng.prefab
[core/table.lua:94]:-->Net Receive: scene.GS2CShowScene = {
|  map_id = 101000
|  scene_id = 12
|  scene_name = "帝都"
}
[net/CNetCtrl.lua:543]:保存war25_07_28(14_04_21)    255    10008
[core/global.lua:59]:<color=#ffeb04>CResCtrl.MoveToSecondary</color>
[net/CNetCtrl.lua:371]:缓存协议    scene    GS2CEnterScene
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardInfo = {
|  breed_val = 7
|  login_day = 7
|  rewarded_day = 127
}
[core/table.lua:94]:-->Net Receive: openui.GS2CRefreshSchedule = {
|  activepoint = 1
|  schstate = {
|  |  activepoint = 1
|  |  count = 157
|  |  done_cnt = 1
|  |  maxtimes = 10
|  |  scheduleid = 1016
|  |  sum = 120
|  }
}
[net/CNetCtrl.lua:371]:缓存协议    partner    GS2CPartnerPropChange
[core/table.lua:94]:-->Net Receive: huodong.GS2CChapterOpen = {
|  chapter = 1
|  level = 8
|  type = 1
}
[net/CNetCtrl.lua:371]:缓存协议    notify    GS2CNotify
[net/CNetCtrl.lua:371]:缓存协议    chat    GS2CConsumeMsg
[core/table.lua:94]:-->Net Receive: huodong.GS2CUpdateChapterTotalStar = {
|  info = {
|  |  chapter = 1
|  |  star = 21
|  |  type = 1
|  }
}
[net/CNetCtrl.lua:371]:缓存协议    notify    GS2CNotify
[net/CNetCtrl.lua:371]:缓存协议    partner    GS2CPartnerPropChange
[core/table.lua:94]:-->Net Receive: huodong.GS2CUpdateChapter = {
|  info = {
|  |  chapter = 1
|  |  level = 7
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
}
[net/CNetCtrl.lua:371]:缓存协议    chat    GS2CConsumeMsg
[core/table.lua:94]:-->Net Receive: huodong.GS2CUpdateChapter = {
|  info = {
|  |  chapter = 1
|  |  fight_time = 1
|  |  level = 7
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
}
[net/CNetCtrl.lua:371]:缓存协议    notify    GS2CNotify
[net/CNetCtrl.lua:371]:缓存协议    chat    GS2CConsumeMsg
[net/CNetCtrl.lua:371]:缓存协议    partner    GS2CPartnerPropChange
[net/CNetCtrl.lua:371]:缓存协议    notify    GS2CNotify
[net/CNetCtrl.lua:371]:缓存协议    chat    GS2CConsumeMsg
[net/CNetCtrl.lua:371]:缓存协议    notify    GS2CNotify
[net/CNetCtrl.lua:371]:缓存协议    chat    GS2CConsumeMsg
[net/CNetCtrl.lua:371]:缓存协议    notify    GS2CNotify
[net/CNetCtrl.lua:371]:缓存协议    player    GS2CPropChange
[net/CNetCtrl.lua:371]:缓存协议    chat    GS2CConsumeMsg
[core/table.lua:94]:-->Net Receive: item.GS2CItemAmount = {
|  amount = 14
|  create_time = 1753682662
|  id = 4
}
[net/CNetCtrl.lua:371]:缓存协议    notify    GS2CNotify
[net/CNetCtrl.lua:371]:缓存协议    chat    GS2CConsumeMsg
[core/table.lua:94]:-->Net Receive: huodong.GS2CChapterFbWinUI = {
|  coin = 170
|  condition = {
|  |  [1] = {
|  |  |  condition = "战斗胜利"
|  |  |  reach = 1
|  |  }
|  |  [2] = {
|  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  reach = 1
|  |  }
|  |  [3] = {
|  |  |  condition = "己方无阵亡单位"
|  |  |  reach = 1
|  |  }
|  }
|  partner_exp = {
|  |  [1] = {
|  |  |  exp = 10470
|  |  |  gain_exp = 280
|  |  |  grade = 10
|  |  |  limit_grade = 16
|  |  |  parid = 1
|  |  }
|  |  [2] = {
|  |  |  exp = 5650
|  |  |  gain_exp = 280
|  |  |  grade = 7
|  |  |  limit_grade = 16
|  |  |  parid = 2
|  |  }
|  |  [3] = {
|  |  |  exp = 3070
|  |  |  gain_exp = 280
|  |  |  grade = 4
|  |  |  limit_grade = 16
|  |  |  parid = 3
|  |  }
|  }
|  player_exp = {
|  |  exp = 15660
|  |  gain_exp = 650
|  |  grade = 11
|  |  limit_grade = 100
|  }
|  stable_reward = {
|  |  [1] = {
|  |  |  amount = 2
|  |  |  sid = "14021"
|  |  |  virtual = 14021
|  |  }
|  }
|  star = 3
|  war_id = 66
|  win = 1
}
[net/CNetCtrl.lua:371]:缓存协议    scene    GS2CEnterAoiBlock
[net/CNetCtrl.lua:371]:缓存协议    scene    GS2CEnterAoiPos
[net/CNetCtrl.lua:371]:缓存协议    scene    GS2CEnterAoiBlock
[net/CNetCtrl.lua:371]:缓存协议    scene    GS2CEnterAoiPos
[net/CNetCtrl.lua:371]:缓存协议    scene    GS2CEnterAoiBlock
[net/CNetCtrl.lua:371]:缓存协议    scene    GS2CEnterAoiPos
[net/CNetCtrl.lua:371]:缓存协议    scene    GS2CEnterAoiBlock
[net/CNetCtrl.lua:371]:缓存协议    scene    GS2CEnterAoiPos
[net/CNetCtrl.lua:371]:缓存协议    scene    GS2CEnterAoiBlock
[net/CNetCtrl.lua:371]:缓存协议    scene    GS2CEnterAoiPos
[net/CNetCtrl.lua:371]:缓存协议    scene    GS2CEnterAoiBlock
[net/CNetCtrl.lua:371]:缓存协议    scene    GS2CEnterAoiPos
[core/global.lua:59]:<color=#ffeb04>WarResultProcess 17 true</color>
[logic/ui/CViewCtrl.lua:94]:CChapterFuBenResultView ShowView
[logic/ui/CViewBase.lua:125]:CChapterFuBenResultView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CChapterFuBenResultView     CloseView
[core/global.lua:59]:<color=#ffeb04>CWarCmd.End</color>
[core/global.lua:59]:<color=#ffeb04>CViewCtrl.CloseAll--> true</color>
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/ui/CViewCtrl"]:280: in function 'CloseAll'
	[string "logic/war/CWarCtrl"]:670: in function 'SwitchEnv'
	[string "logic/war/CWarCtrl"]:651: in function <[string "logic/war/CWarCtrl"]:626>
	[C]: in function 'xxpcall'
	[string "logic/war/CWarCtrl"]:1525: in function 'ProcessActionList'
	[string "logic/war/CWarCtrl"]:1540: in function 'UpdateActions'
	[string "logic/war/CWarCtrl"]:1494: in function 'Update'
	[string "main"]:40: in function <[string "main"]:31>
[logic/ui/CViewCtrl.lua:94]:CMainMenuView ShowView
[logic/ui/CViewCtrl.lua:94]:CChapterFuBenMainView ShowView
[core/table.lua:94]:warend 执行缓存协议:31 true = {
|  [1] = {
|  |  [1] = "scene"
|  |  [2] = "GS2CEnterScene"
|  |  [3] = {
|  |  |  eid = 32
|  |  |  pos_info = {
|  |  |  |  face_y = 237812
|  |  |  |  x = 44621
|  |  |  |  y = 26410
|  |  |  }
|  |  |  scene_id = 12
|  |  }
|  }
|  [10] = {
|  |  [1] = "partner"
|  |  [2] = "GS2CPartnerPropChange"
|  |  [3] = {
|  |  |  partner_info = {
|  |  |  |  exp = 3350
|  |  |  |  mask = "80"
|  |  |  }
|  |  |  partnerid = 3
|  |  }
|  }
|  [11] = {
|  |  [1] = "notify"
|  |  [2] = "GS2CNotify"
|  |  [3] = {
|  |  |  cmd = "#B重华#n获得#hexp#G280#n"
|  |  }
|  }
|  [12] = {
|  |  [1] = "chat"
|  |  [2] = "GS2CConsumeMsg"
|  |  [3] = {
|  |  |  content = "#B重华#n获得#hexp#G280#n"
|  |  |  type = 6
|  |  }
|  }
|  [13] = {
|  |  [1] = "notify"
|  |  [2] = "GS2CNotify"
|  |  [3] = {
|  |  |  cmd = "#B马面面#n获得#hexp#G280#n"
|  |  }
|  }
|  [14] = {
|  |  [1] = "chat"
|  |  [2] = "GS2CConsumeMsg"
|  |  [3] = {
|  |  |  content = "#B马面面#n获得#hexp#G280#n"
|  |  |  type = 6
|  |  }
|  }
|  [15] = {
|  |  [1] = "notify"
|  |  [2] = "GS2CNotify"
|  |  [3] = {
|  |  |  cmd = "#B蛇姬#n获得#hexp#G280#n"
|  |  }
|  }
|  [16] = {
|  |  [1] = "player"
|  |  [2] = "GS2CPropChange"
|  |  [3] = {
|  |  |  role = {
|  |  |  |  active = 7
|  |  |  |  coin = 758450
|  |  |  |  energy = 157
|  |  |  |  exp = 16310
|  |  |  |  mask = "100002000000060"
|  |  |  }
|  |  }
|  }
|  [17] = {
|  |  [1] = "chat"
|  |  [2] = "GS2CConsumeMsg"
|  |  [3] = {
|  |  |  content = "#B蛇姬#n获得#hexp#G280#n"
|  |  |  type = 6
|  |  }
|  }
|  [18] = {
|  |  [1] = "notify"
|  |  [2] = "GS2CNotify"
|  |  [3] = {
|  |  |  cmd = "获得 #B[深蓝琥珀] x 2#n"
|  |  }
|  }
|  [19] = {
|  |  [1] = "chat"
|  |  [2] = "GS2CConsumeMsg"
|  |  [3] = {
|  |  |  content = "获得 #B[深蓝琥珀] x 2#n"
|  |  |  type = 6
|  |  }
|  }
|  [2] = {
|  |  [1] = "partner"
|  |  [2] = "GS2CPartnerPropChange"
|  |  [3] = {
|  |  |  partner_info = {
|  |  |  |  exp = 10750
|  |  |  |  mask = "80"
|  |  |  }
|  |  |  partnerid = 1
|  |  }
|  }
|  [20] = {
|  |  [1] = "scene"
|  |  [2] = "GS2CEnterAoiBlock"
|  |  [3] = {
|  |  |  aoi_npc = {
|  |  |  |  block = {
|  |  |  |  |  mask = "10e"
|  |  |  |  |  model_info = {
|  |  |  |  |  |  color = {
|  |  |  |  |  |  |  [1] = 0
|  |  |  |  |  |  |  [2] = 0
|  |  |  |  |  |  |  [3] = 0
|  |  |  |  |  |  |  [4] = 0
|  |  |  |  |  |  }
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 1102
|  |  |  |  |  }
|  |  |  |  |  name = "峰馆长"
|  |  |  |  |  trapmine = {}
|  |  |  |  }
|  |  |  |  npcid = 2
|  |  |  |  npctype = 5040
|  |  |  }
|  |  |  eid = 1
|  |  |  scene_id = 12
|  |  |  type = 2
|  |  }
|  }
|  [21] = {
|  |  [1] = "scene"
|  |  [2] = "GS2CEnterAoiPos"
|  |  [3] = {
|  |  |  eid = 1
|  |  |  pos_info = {
|  |  |  |  x = 31800
|  |  |  |  y = 17600
|  |  |  }
|  |  |  scene_id = 12
|  |  |  type = 2
|  |  }
|  }
|  [22] = {
|  |  [1] = "scene"
|  |  [2] = "GS2CEnterAoiBlock"
|  |  [3] = {
|  |  |  aoi_npc = {
|  |  |  |  block = {
|  |  |  |  |  mask = "10e"
|  |  |  |  |  model_info = {
|  |  |  |  |  |  color = {
|  |  |  |  |  |  |  [1] = 0
|  |  |  |  |  |  |  [2] = 0
|  |  |  |  |  |  |  [3] = 0
|  |  |  |  |  |  |  [4] = 0
|  |  |  |  |  |  }
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 1016
|  |  |  |  |  }
|  |  |  |  |  name = "水生"
|  |  |  |  |  trapmine = {}
|  |  |  |  }
|  |  |  |  npcid = 196
|  |  |  |  npctype = 5009
|  |  |  }
|  |  |  eid = 17
|  |  |  scene_id = 12
|  |  |  type = 2
|  |  }
|  }
|  [23] = {
|  |  [1] = "scene"
|  |  [2] = "GS2CEnterAoiPos"
|  |  [3] = {
|  |  |  eid = 17
|  |  |  pos_info = {
|  |  |  |  x = 24000
|  |  |  |  y = 22000
|  |  |  }
|  |  |  scene_id = 12
|  |  |  type = 2
|  |  }
|  }
|  [24] = {
|  |  [1] = "scene"
|  |  [2] = "GS2CEnterAoiBlock"
|  |  [3] = {
|  |  |  aoi_npc = {
|  |  |  |  block = {
|  |  |  |  |  mask = "10e"
|  |  |  |  |  model_info = {
|  |  |  |  |  |  color = {
|  |  |  |  |  |  |  [1] = 0
|  |  |  |  |  |  |  [2] = 0
|  |  |  |  |  |  |  [3] = 0
|  |  |  |  |  |  |  [4] = 0
|  |  |  |  |  |  }
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 1011
|  |  |  |  |  }
|  |  |  |  |  name = "遥"
|  |  |  |  |  trapmine = {}
|  |  |  |  }
|  |  |  |  npcid = 72
|  |  |  |  npctype = 5005
|  |  |  }
|  |  |  eid = 13
|  |  |  scene_id = 12
|  |  |  type = 2
|  |  }
|  }
|  [25] = {
|  |  [1] = "scene"
|  |  [2] = "GS2CEnterAoiPos"
|  |  [3] = {
|  |  |  eid = 13
|  |  |  pos_info = {
|  |  |  |  x = 45500
|  |  |  |  y = 20000
|  |  |  }
|  |  |  scene_id = 12
|  |  |  type = 2
|  |  }
|  }
|  [26] = {
|  |  [1] = "scene"
|  |  [2] = "GS2CEnterAoiBlock"
|  |  [3] = {
|  |  |  aoi_npc = {
|  |  |  |  block = {
|  |  |  |  |  mask = "10e"
|  |  |  |  |  model_info = {
|  |  |  |  |  |  color = {
|  |  |  |  |  |  |  [1] = 0
|  |  |  |  |  |  |  [2] = 0
|  |  |  |  |  |  |  [3] = 0
|  |  |  |  |  |  |  [4] = 0
|  |  |  |  |  |  }
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 1010
|  |  |  |  |  }
|  |  |  |  |  name = "丽丝"
|  |  |  |  |  trapmine = {}
|  |  |  |  }
|  |  |  |  npcid = 69
|  |  |  |  npctype = 5004
|  |  |  }
|  |  |  eid = 12
|  |  |  scene_id = 12
|  |  |  type = 2
|  |  }
|  }
|  [27] = {
|  |  [1] = "scene"
|  |  [2] = "GS2CEnterAoiPos"
|  |  [3] = {
|  |  |  eid = 12
|  |  |  pos_info = {
|  |  |  |  x = 45600
|  |  |  |  y = 26900
|  |  |  }
|  |  |  scene_id = 12
|  |  |  type = 2
|  |  }
|  }
|  [28] = {
|  |  [1] = "scene"
|  |  [2] = "GS2CEnterAoiBlock"
|  |  [3] = {
|  |  |  aoi_npc = {
|  |  |  |  block = {
|  |  |  |  |  mask = "10e"
|  |  |  |  |  model_info = {
|  |  |  |  |  |  color = {
|  |  |  |  |  |  |  [1] = 0
|  |  |  |  |  |  |  [2] = 0
|  |  |  |  |  |  |  [3] = 0
|  |  |  |  |  |  |  [4] = 0
|  |  |  |  |  |  }
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 417
|  |  |  |  |  }
|  |  |  |  |  name = "松姑子"
|  |  |  |  |  trapmine = {}
|  |  |  |  }
|  |  |  |  npcid = 220
|  |  |  |  npctype = 5019
|  |  |  }
|  |  |  eid = 21
|  |  |  scene_id = 12
|  |  |  type = 2
|  |  }
|  }
|  [29] = {
|  |  [1] = "scene"
|  |  [2] = "GS2CEnterAoiPos"
|  |  [3] = {
|  |  |  eid = 21
|  |  |  pos_info = {
|  |  |  |  x = 42300
|  |  |  |  y = 24600
|  |  |  }
|  |  |  scene_id = 12
|  |  |  type = 2
|  |  }
|  }
|  [3] = {
|  |  [1] = "notify"
|  |  [2] = "GS2CNotify"
|  |  [3] = {
|  |  |  cmd = "获得#exp#G650#n"
|  |  }
|  }
|  [30] = {
|  |  [1] = "scene"
|  |  [2] = "GS2CEnterAoiBlock"
|  |  [3] = {
|  |  |  aoi_npc = {
|  |  |  |  block = {
|  |  |  |  |  mask = "10e"
|  |  |  |  |  model_info = {
|  |  |  |  |  |  color = {
|  |  |  |  |  |  |  [1] = 0
|  |  |  |  |  |  |  [2] = 0
|  |  |  |  |  |  |  [3] = 0
|  |  |  |  |  |  |  [4] = 0
|  |  |  |  |  |  }
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 1012
|  |  |  |  |  }
|  |  |  |  |  name = "克瑞斯汀"
|  |  |  |  |  trapmine = {}
|  |  |  |  }
|  |  |  |  npcid = 24
|  |  |  |  npctype = 5048
|  |  |  }
|  |  |  eid = 7
|  |  |  scene_id = 12
|  |  |  type = 2
|  |  }
|  }
|  [31] = {
|  |  [1] = "scene"
|  |  [2] = "GS2CEnterAoiPos"
|  |  [3] = {
|  |  |  eid = 7
|  |  |  pos_info = {
|  |  |  |  x = 20200
|  |  |  |  y = 21100
|  |  |  }
|  |  |  scene_id = 12
|  |  |  type = 2
|  |  }
|  }
|  [4] = {
|  |  [1] = "chat"
|  |  [2] = "GS2CConsumeMsg"
|  |  [3] = {
|  |  |  content = "获得#exp#G650#n"
|  |  |  type = 6
|  |  }
|  }
|  [5] = {
|  |  [1] = "notify"
|  |  [2] = "GS2CNotify"
|  |  [3] = {
|  |  |  cmd = "获得了#w1#G170#n"
|  |  }
|  }
|  [6] = {
|  |  [1] = "partner"
|  |  [2] = "GS2CPartnerPropChange"
|  |  [3] = {
|  |  |  partner_info = {
|  |  |  |  exp = 5930
|  |  |  |  mask = "80"
|  |  |  }
|  |  |  partnerid = 2
|  |  }
|  }
|  [7] = {
|  |  [1] = "chat"
|  |  [2] = "GS2CConsumeMsg"
|  |  [3] = {
|  |  |  content = "获得了#w1#G170#n"
|  |  |  type = 6
|  |  }
|  }
|  [8] = {
|  |  [1] = "notify"
|  |  [2] = "GS2CNotify"
|  |  [3] = {
|  |  |  cmd = "获得了#w5#G1#n"
|  |  }
|  }
|  [9] = {
|  |  [1] = "chat"
|  |  [2] = "GS2CConsumeMsg"
|  |  [3] = {
|  |  |  content = "获得了#w5#G1#n"
|  |  |  type = 6
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>=00000=  userdata: 0x7f2ae5f0 nil</color>
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 10750
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 5930
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 3350
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  active = 7
|  coin = 758450
|  energy = 157
|  exp = 16310
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1102
|  }
|  name = "峰馆长"
|  trapmine = {}
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1016
|  }
|  name = "水生"
|  trapmine = {}
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1011
|  }
|  name = "遥"
|  trapmine = {}
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1010
|  }
|  name = "丽丝"
|  trapmine = {}
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 417
|  }
|  name = "松姑子"
|  trapmine = {}
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1012
|  }
|  name = "克瑞斯汀"
|  trapmine = {}
}
[logic/ui/CSprite.lua:159]:DynamicSprite dSprInfo    Item    10012
[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
[core/global.lua:59]:<color=#ffeb04>读取地图： 1020</color>
[core/global.lua:59]:<color=#ffeb04>Unity2017.4.30f1 localScale</color>
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/global.lua:59]:<color=#ffeb04>地图加载完成: 1020 ,当前地图: 5020</color>
[core/global.lua:59]:<color=#ffeb04>删除地图: 5020</color>
[core/table.lua:94]:Table = {
|  cur_mapid = 101000
|  cur_posx = 47.2
|  cur_posy = 23.3
|  mapid = 101000
|  npctype = 11432
|  pos_x = 44700
|  pos_y = 26500
}
[core/table.lua:94]:Table = {}
[core/table.lua:94]:<--Net Send: task.C2GSSyncTraceInfo = {
|  cur_mapid = 101000
|  cur_posx = 47200
|  cur_posy = 23300
|  taskid = 10379
}
[logic/ui/CViewBase.lua:125]:CChapterFuBenMainView LoadDone!
[core/global.lua:59]:<color=#ffeb04>剧情章节： 1</color>
[core/table.lua:94]:<--Net Send: huodong.C2GSGetChapterInfo = {
|  chapter = 1
|  type = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChapterInfo = {
|  info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 4
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = 1
|  |  |  level = 2
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  chapter = 1
|  |  |  level = 6
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  chapter = 1
|  |  |  level = 8
|  |  |  open = 1
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  chapter = 1
|  |  |  level = 3
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  chapter = 1
|  |  |  level = 1
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [7] = {
|  |  |  chapter = 1
|  |  |  level = 5
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  |  [8] = {
|  |  |  chapter = 1
|  |  |  fight_time = 1
|  |  |  level = 7
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  star = 3
|  |  |  star_condition = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = "战斗胜利"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  |  reach = 1
|  |  |  |  }
|  |  |  }
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-刷新章节信息 = {
|  [1] = {
|  |  chapter = 1
|  |  level = 1
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [2] = {
|  |  chapter = 1
|  |  level = 2
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [3] = {
|  |  chapter = 1
|  |  level = 3
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [4] = {
|  |  chapter = 1
|  |  level = 4
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [5] = {
|  |  chapter = 1
|  |  level = 5
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [6] = {
|  |  chapter = 1
|  |  level = 6
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [7] = {
|  |  chapter = 1
|  |  fight_time = 1
|  |  level = 7
|  |  open = 1
|  |  pass = 1
|  |  star = 3
|  |  star_condition = {
|  |  |  [1] = {
|  |  |  |  condition = "战斗胜利"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  |  reach = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  condition = "己方无阵亡单位"
|  |  |  |  reach = 1
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  [8] = {
|  |  chapter = 1
|  |  level = 8
|  |  open = 1
|  |  type = 1
|  }
}
[core/table.lua:94]:-刷新关卡信息 = {
|  chapter = 1
|  fight_time = 1
|  level = 7
|  open = 1
|  pass = 1
|  star = 3
|  star_condition = {
|  |  [1] = {
|  |  |  condition = "战斗胜利"
|  |  |  reach = 1
|  |  }
|  |  [2] = {
|  |  |  condition = "阵亡伙伴不超过1个"
|  |  |  reach = 1
|  |  }
|  |  [3] = {
|  |  |  condition = "己方无阵亡单位"
|  |  |  reach = 1
|  |  }
|  }
|  type = 1
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682667
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:04:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CChapterFuBenMainView     CloseView
[logic/ui/CViewCtrl.lua:94]:CLimitRewardView ShowView
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[logic/ui/CViewBase.lua:125]:CLimitRewardView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 2
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753682677
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 14:04:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CLimitRewardView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}

谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000446F64CB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000446F63B4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447B84B5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004D1B50B3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004D1B51C5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000447B8BC3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000447B8614 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000447B8847 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000446F64CB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000446F63B4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447B84B5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004D1B50B3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004D1B4F0F (Mono JIT Code) [GoogleEventHandle.cs:134] GoogleEventHandle:GooglePayInit () 
0x000000004D1B49C4 (Mono JIT Code) [ShareSDKManagerWrap.cs:85] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x000000004A386A33 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16DCF1F7 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004D19433C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004D1941BA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004D1A4177 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004D1A3FC0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000004D1A31AC (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x000000004D1A2E3E (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000C274C2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000446F64CB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000446F63B4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447B84B5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004D1B50B3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004D1B4F0F (Mono JIT Code) [GoogleEventHandle.cs:134] GoogleEventHandle:GooglePayInit () 
0x000000004D1B49C4 (Mono JIT Code) [ShareSDKManagerWrap.cs:85] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x000000004A386A33 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16DCF1F7 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004D19433C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004D1941BA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004D1A4177 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004D1A3FC0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000004D1A31AC (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x000000004D1A2E3E (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000C274C2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:217: in function <[string "logic/base/CResCtrl"]:212>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000446F64CB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000446F63B4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447B84B5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004D1B50B3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004D1B51C5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000447B8BC3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000447B8614 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000447B8847 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000446F64CB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000446F63B4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447B84B5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004D1B50B3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000006E89BF80 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000006E89BD17 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C32583 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D771D9 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004D19433C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004D1941BA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004D1A4177 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004D1A3FC0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000006325A7D4 (Mono JIT Code) [LuaMain.cs:114] LuaMain:Update () 
0x0000000000C274C2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:217: in function <[string "logic/base/CResCtrl"]:212>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000446F64CB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000446F63B4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447B84B5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004D1B50B3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000006E89BF80 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000006E89BD17 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C32583 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D771D9 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004D19433C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004D1941BA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004D1A4177 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004D1A3FC0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000006325A7D4 (Mono JIT Code) [LuaMain.cs:114] LuaMain:Update () 
0x0000000000C274C2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:217: in function <[string "logic/base/CResCtrl"]:212>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>

谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470813B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000044708024 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447C9F85 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004CDBA2B3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004CDBA3C5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000447CA693 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000447CA0E4 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000447CA317 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470813B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000044708024 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447C9F85 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004CDBA2B3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004CDBA10F (Mono JIT Code) [GoogleEventHandle.cs:134] GoogleEventHandle:GooglePayInit () 
0x000000004CDB9BC4 (Mono JIT Code) [ShareSDKManagerWrap.cs:85] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x000000004A389603 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16DCF1F7 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004CD993DC (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004CD9925A (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004CDA9107 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004CDA8F50 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000004CDA813C (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x000000004CDA7DCE (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000C274C2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470813B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000044708024 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447C9F85 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004CDBA2B3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004CDBA10F (Mono JIT Code) [GoogleEventHandle.cs:134] GoogleEventHandle:GooglePayInit () 
0x000000004CDB9BC4 (Mono JIT Code) [ShareSDKManagerWrap.cs:85] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x000000004A389603 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16DCF1F7 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004CD993DC (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004CD9925A (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004CDA9107 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004CDA8F50 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000004CDA813C (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x000000004CDA7DCE (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000C274C2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:217: in function <[string "logic/base/CResCtrl"]:212>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470813B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000044708024 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447C9F85 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004CDBA2B3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004CDBA3C5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000447CA693 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000447CA0E4 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000447CA317 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470813B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000044708024 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447C9F85 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004CDBA2B3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x0000000074BAE260 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x0000000074BADFF7 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C38C53 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D771D9 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004CD993DC (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004CD9925A (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004CDA9107 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004CDA8F50 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x0000000063043EF4 (Mono JIT Code) [LuaMain.cs:114] LuaMain:Update () 
0x0000000000C274C2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:217: in function <[string "logic/base/CResCtrl"]:212>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470813B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000044708024 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447C9F85 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004CDBA2B3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x0000000074BAE260 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x0000000074BADFF7 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C38C53 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D771D9 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004CD993DC (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004CD9925A (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004CDA9107 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004CDA8F50 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x0000000063043EF4 (Mono JIT Code) [LuaMain.cs:114] LuaMain:Update () 
0x0000000000C274C2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:217: in function <[string "logic/base/CResCtrl"]:212>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>

谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470809B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000044707F84 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447C9F55 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004CDBA643 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004CDBA755 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000447CA663 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000447CA0B4 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000447CA2E7 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470809B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000044707F84 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447C9F55 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004CDBA643 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004CDBA49F (Mono JIT Code) [GoogleEventHandle.cs:134] GoogleEventHandle:GooglePayInit () 
0x000000004CDB9F54 (Mono JIT Code) [ShareSDKManagerWrap.cs:85] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x000000004A389E93 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16DCF1F7 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004CD999DC (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004CD9985A (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004CDA9717 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004CDA9560 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000004CDA874C (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x000000004CDA83DE (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000C274C2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470809B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000044707F84 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447C9F55 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004CDBA643 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004CDBA49F (Mono JIT Code) [GoogleEventHandle.cs:134] GoogleEventHandle:GooglePayInit () 
0x000000004CDB9F54 (Mono JIT Code) [ShareSDKManagerWrap.cs:85] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x000000004A389E93 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16DCF1F7 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004CD999DC (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004CD9985A (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004CDA9717 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004CDA9560 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000004CDA874C (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x000000004CDA83DE (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000C274C2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:217: in function <[string "logic/base/CResCtrl"]:212>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470809B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000044707F84 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447C9F55 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004CDBA643 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004CDBA755 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000447CA663 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000447CA0B4 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000447CA2E7 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470809B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000044707F84 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447C9F55 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004CDBA643 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000006E665B60 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000006E6658F7 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C38C83 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D771D9 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004CD999DC (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004CD9985A (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004CDA9717 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004CDA9560 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x00000000630492F4 (Mono JIT Code) [LuaMain.cs:114] LuaMain:Update () 
0x0000000000C274C2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:217: in function <[string "logic/base/CResCtrl"]:212>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470809B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000044707F84 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447C9F55 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004CDBA643 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000006E665B60 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000006E6658F7 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C38C83 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D771D9 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004CD999DC (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004CD9985A (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004CDA9717 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004CDA9560 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x00000000630492F4 (Mono JIT Code) [LuaMain.cs:114] LuaMain:Update () 
0x0000000000C274C2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:217: in function <[string "logic/base/CResCtrl"]:212>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>


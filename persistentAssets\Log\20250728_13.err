谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E782DE5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000447CE8B3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000447CE304 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000447CE537 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E782B2F (Mono JIT Code) [GoogleEventHandle.cs:134] GoogleEventHandle:GooglePayInit () 
0x000000004E7825E4 (Mono JIT Code) [ShareSDKManagerWrap.cs:85] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x0000000046E42013 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16DCF1F7 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004E76206C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004E761EEA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004E771D97 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004E771BE0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000004E770DCC (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x000000004E770A5E (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E782B2F (Mono JIT Code) [GoogleEventHandle.cs:134] GoogleEventHandle:GooglePayInit () 
0x000000004E7825E4 (Mono JIT Code) [ShareSDKManagerWrap.cs:85] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x0000000046E42013 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16DCF1F7 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004E76206C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004E761EEA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004E771D97 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004E771BE0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000004E770DCC (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x000000004E770A5E (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:217: in function <[string "logic/base/CResCtrl"]:212>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E782DE5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000447CE8B3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000447CE304 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000447CE537 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000007AD002F0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000007AD00087 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C41013 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D771D9 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004E76206C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004E761EEA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004E771D97 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004E771BE0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000006EC3C034 (Mono JIT Code) [LuaMain.cs:114] LuaMain:Update () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:217: in function <[string "logic/base/CResCtrl"]:212>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000007AD002F0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000007AD00087 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C41013 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D771D9 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004E76206C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004E761EEA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004E771D97 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004E771BE0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000006EC3C034 (Mono JIT Code) [LuaMain.cs:114] LuaMain:Update () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:217: in function <[string "logic/base/CResCtrl"]:212>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>

[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E782DE5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000447CE8B3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000447CE304 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000447CE537 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000007AD002F0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000007AD00087 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C41013 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D76C19 ((<unknown>)) 
0x00007FFF16D76D8F ((<unknown>)) 
0x00007FFF16D7A41B ((<unknown>)) 
0x00007FFF16D729C7 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004E76206C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004E761EEA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004E771D97 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004E771BE0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000007AC99D60 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x000000007AC99B96 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x000000007AC99AC0 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x000000007AC999F1 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x000000007AC7DA31 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x000000007AC7D770 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x000000007AC97B69 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x000000007AC7EA12 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x00000000447CCEA3 (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x000000004479153E (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x000000004478FBE1 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x000000004478FA76 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000007AD002F0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000007AD00087 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C41013 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D76C19 ((<unknown>)) 
0x00007FFF16D76D8F ((<unknown>)) 
0x00007FFF16D7A41B ((<unknown>)) 
0x00007FFF16D729C7 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004E76206C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004E761EEA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004E771D97 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004E771BE0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000007AC99D60 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x000000007AC99B96 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x000000007AC99AC0 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x000000007AC999F1 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x000000007AC7DA31 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x000000007AC7D770 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x000000007AC97B69 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x000000007AC7EA12 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x00000000447CCEA3 (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x000000004479153E (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x000000004478FBE1 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x000000004478FA76 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>

[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E782DE5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000447CE8B3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000447CE304 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000447CE537 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000007AD002F0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000007AD00087 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C41013 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D76C19 ((<unknown>)) 
0x00007FFF16D76D8F ((<unknown>)) 
0x00007FFF16D7A41B ((<unknown>)) 
0x00007FFF16D729C7 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004E76206C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004E761EEA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004E771D97 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004E771BE0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000007AC99D60 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x000000007AC99B96 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x000000007AC99AC0 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x000000007AC999F1 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x000000007AC7DA31 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x000000007AC7D770 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x000000007AC97B69 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x000000007AC7EA12 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x00000000447CCEA3 (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x000000004479153E (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x000000004478FBE1 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x000000004478FA76 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000007AD002F0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000007AD00087 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C41013 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D76C19 ((<unknown>)) 
0x00007FFF16D76D8F ((<unknown>)) 
0x00007FFF16D7A41B ((<unknown>)) 
0x00007FFF16D729C7 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004E76206C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004E761EEA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004E771D97 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004E771BE0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000007AC99D60 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x000000007AC99B96 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x000000007AC99AC0 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x000000007AC999F1 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x000000007AC7DA31 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x000000007AC7D770 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x000000007AC97B69 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x000000007AC7EA12 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x00000000447CCEA3 (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x000000004479153E (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x000000004478FBE1 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x000000004478FA76 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>

[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E782DE5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000447CE8B3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000447CE304 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000447CE537 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000007AD002F0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000007AD00087 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C41013 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D76C19 ((<unknown>)) 
0x00007FFF16D76D8F ((<unknown>)) 
0x00007FFF16D7A41B ((<unknown>)) 
0x00007FFF16D729C7 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004E76206C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004E761EEA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004E771D97 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004E771BE0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000007AC99D60 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x000000007AC99B96 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x000000007AC99AC0 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x000000007AC999F1 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x000000007AC7DA31 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x000000007AC7D770 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x000000007AC97B69 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x000000007AC7EA12 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x00000000447CCEA3 (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x000000004479153E (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x000000004478FBE1 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x000000004478FA76 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000007AD002F0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000007AD00087 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C41013 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D76C19 ((<unknown>)) 
0x00007FFF16D76D8F ((<unknown>)) 
0x00007FFF16D7A41B ((<unknown>)) 
0x00007FFF16D729C7 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004E76206C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004E761EEA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004E771D97 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004E771BE0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000007AC99D60 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x000000007AC99B96 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x000000007AC99AC0 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x000000007AC999F1 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x000000007AC7DA31 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x000000007AC7D770 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x000000007AC97B69 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x000000007AC7EA12 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x00000000447CCEA3 (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x000000004479153E (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x000000004478FBE1 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x000000004478FA76 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>

[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E782DE5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000447CE8B3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000447CE304 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000447CE537 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000007AD002F0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000007AD00087 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C41013 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D76C19 ((<unknown>)) 
0x00007FFF16D76D8F ((<unknown>)) 
0x00007FFF16D7A41B ((<unknown>)) 
0x00007FFF16D729C7 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004E76206C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004E761EEA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004E771D97 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004E771BE0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000007AC99D60 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x000000007AC99B96 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x000000007AC99AC0 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x000000007AC999F1 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x000000007AC7DA31 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x000000007AC7D770 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x000000007AC97B69 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x000000007AC7EA12 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x00000000447CCEA3 (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x000000004479153E (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x000000004478FBE1 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x000000004478FA76 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000007AD002F0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000007AD00087 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C41013 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D76C19 ((<unknown>)) 
0x00007FFF16D76D8F ((<unknown>)) 
0x00007FFF16D7A41B ((<unknown>)) 
0x00007FFF16D729C7 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004E76206C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004E761EEA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004E771D97 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004E771BE0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000007AC99D60 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x000000007AC99B96 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x000000007AC99AC0 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x000000007AC999F1 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x000000007AC7DA31 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x000000007AC7D770 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x000000007AC97B69 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x000000007AC7EA12 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x00000000447CCEA3 (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x000000004479153E (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x000000004478FBE1 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x000000004478FA76 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:123: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>

[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E782DE5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000447CE8B3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000447CE304 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000447CE537 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000007AD002F0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000007AD00087 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C41013 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D771D9 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004E76206C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004E761EEA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004E771D97 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004E771BE0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000006EC3C034 (Mono JIT Code) [LuaMain.cs:114] LuaMain:Update () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000007AD002F0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000007AD00087 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C41013 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D771D9 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004E76206C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004E761EEA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004E771D97 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004E771BE0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000006EC3C034 (Mono JIT Code) [LuaMain.cs:114] LuaMain:Update () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>

[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E782DE5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000447CE8B3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000447CE304 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000447CE537 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000007AD002F0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000007AD00087 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C41013 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D771D9 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004E76206C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004E761EEA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004E771D97 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004E771BE0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000006EC3C034 (Mono JIT Code) [LuaMain.cs:114] LuaMain:Update () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004470E36B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004470E254 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000447CE1A5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E782CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000007AD002F0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x000000007AD00087 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x0000000046C41013 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D76B49 ((<unknown>)) 
0x00007FFF16D771D9 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000004E76206C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004E761EEA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004E771D97 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004E771BE0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000006EC3C034 (Mono JIT Code) [LuaMain.cs:114] LuaMain:Update () 
0x0000000000C274E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


[string "logic/base/CGameObjContainer"]:28: GetObject CButton Wrong! objid = 9
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: in function 'error'
	[string "logic/base/CGameObjContainer"]:28: in function 'NewUI'
	[string "logic/mainmenu/CMainMenuView"]:27: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>


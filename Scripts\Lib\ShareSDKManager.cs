using System;
using System.Collections;
using System.Collections.Generic;
using cn.sharesdk.unity3d;
using UnityEngine;
using LuaInterface;

public class ShareSDKManager : MonoBehaviour
{
	public bool initDone = false;
	private ShareSDK _shareSDK;
	static private LuaFunction _luaCallback;
	static private LuaFunction _luaQuickLoginCallback;
	static private LuaFunction _luaQuickSwitchAccountCallback;
	static private LuaFunction _luaQuickLogoutCallback;
	static private LuaFunction _luaQuickExitGameCallback;
	static private LuaFunction _luaQuickCancellationAccountCallback;
	enum NotiftType
	{
		Auth = 1,
		Share = 2,
		ShowUser = 3,
		GetFriends = 4,
		FollowFriend = 5,
	}

	//分享回调
	static private void FUNCTAG_CallShareFunc(NotiftType notiftType, int reqID, ResponseState state, PlatformType type, Hashtable data)
	{
		var jsonData = data.toJson();
		Debug.Log("Unity Share Callback:"+ _luaCallback+ "state:"+ state+"  data:"+jsonData);
		if (_luaCallback != null)
		{
			_luaCallback.BeginPCall();
			_luaCallback.Push(notiftType);
			_luaCallback.Push(reqID);
			_luaCallback.Push(state);
			_luaCallback.Push(type);
			_luaCallback.Push(jsonData);
			_luaCallback.PCall();
			_luaCallback.EndPCall();
		}
	}

	static private void FUNCTAG_Login_CallShareFunc(string accName,string pwd)
	{
		Debug.Log("Unity Share Callback:" + _luaQuickLoginCallback + "accName:" + accName + "  pwd:" + pwd);
		if (_luaQuickLoginCallback != null)
		{
			_luaQuickLoginCallback.BeginPCall();
			_luaQuickLoginCallback.Push(accName);
			_luaQuickLoginCallback.Push(pwd);
			_luaQuickLoginCallback.PCall();
			_luaQuickLoginCallback.EndPCall();
		}
	}

	static private void FUNCTAG_LoginOut_CallShareFunc(string accName, string pwd)
	{
		Debug.Log("Unity Share Callback:" + _luaQuickLoginCallback + "accName:" + accName + "  pwd:" + pwd);
		if (_luaQuickLogoutCallback != null)
		{
			_luaQuickLogoutCallback.BeginPCall();
			_luaQuickLogoutCallback.Push(accName);
			_luaQuickLogoutCallback.Push(pwd);
			_luaQuickLogoutCallback.PCall();
			_luaQuickLogoutCallback.EndPCall();
		}
	}

	static private void FUNCTAG_Switch_CallShareFunc(string accName, string pwd)
	{
		Debug.Log("Unity Share Callback:" + _luaQuickSwitchAccountCallback + "accName:" + accName + "  pwd:" + pwd);
		if (_luaQuickSwitchAccountCallback != null)
		{
			_luaQuickSwitchAccountCallback.BeginPCall();
			_luaQuickSwitchAccountCallback.Push(accName);
			_luaQuickSwitchAccountCallback.Push(pwd);
			_luaQuickSwitchAccountCallback.PCall();
			_luaQuickSwitchAccountCallback.EndPCall();
		}
	}

	static private void FUNCTAG_Cancellation_CallShareFunc(string accName, string pwd)
	{
		Debug.Log("Unity Share Callback cancellation");
		if (_luaQuickCancellationAccountCallback != null)
		{
			_luaQuickCancellationAccountCallback.BeginPCall();
			//_luaQuickCancellationAccountCallback.Push(accName);
			//_luaQuickCancellationAccountCallback.Push(pwd);
			_luaQuickCancellationAccountCallback.PCall();
			_luaQuickCancellationAccountCallback.EndPCall();
		}
	}

	static private void FUNCTAG_Exit_CallShareFunc(string accName, string pwd)
	{
		Debug.Log("Unity Share Callback:" + _luaQuickLoginCallback + "accName:" + accName + "  pwd:" + pwd);
		if (_luaQuickExitGameCallback != null)
		{
			_luaQuickExitGameCallback.BeginPCall();
			_luaQuickExitGameCallback.Push(accName);
			_luaQuickExitGameCallback.Push(pwd);
			_luaQuickExitGameCallback.PCall();
			_luaQuickExitGameCallback.EndPCall();
		}
	}

	public void FUNCTAG_InitShare(ShareSDK instance, LuaFunction cb)
	{
		if (_luaCallback != null)
		{
			_luaCallback.Dispose();
		}
		_luaCallback = cb;
		_shareSDK = instance;
		_shareSDK.authHandler = (int reqID, ResponseState state, PlatformType type, Hashtable data) =>
		{
			FUNCTAG_CallShareFunc(NotiftType.Auth, reqID, state, type, data);
		};
		_shareSDK.shareHandler = (int reqID, ResponseState state, PlatformType type, Hashtable data) =>
		{
			FUNCTAG_CallShareFunc(NotiftType.Share, reqID, state, type, data);
		};
		_shareSDK.showUserHandler = (int reqID, ResponseState state, PlatformType type, Hashtable data) =>
		{
			FUNCTAG_CallShareFunc(NotiftType.ShowUser, reqID, state, type, data);
		};
		_shareSDK.getFriendsHandler = (int reqID, ResponseState state, PlatformType type, Hashtable data) =>
		{
			FUNCTAG_CallShareFunc(NotiftType.GetFriends, reqID, state, type, data);
		};
		_shareSDK.followFriendHandler = (int reqID, ResponseState state, PlatformType type, Hashtable data) =>
		{
			FUNCTAG_CallShareFunc(NotiftType.FollowFriend, reqID, state, type, data);
		};

#if QUICKSDK
		quicksdk.QuickSDK.getInstance().setShareSDK(_shareSDK);
#endif

		/// <summary>
		///     设置平台配置，其实只有 ios 用到
		/// </summary>
#if !UNITY_EDITOR && (UNITY_ANDROID || UNITY_IPHONE)
        _shareSDK.InitSDK(ShareSDKConfig.GetAppKey());
        _shareSDK.SetPlatformConfig(ShareSDKConfig.GetConfigEx());
#endif
		initDone = true;
	}

	public void FUNCTAG_Quick_Login_call(ShareSDK instance, LuaFunction cb)
	{
		if (_luaQuickLoginCallback != null)
		{
			_luaQuickLoginCallback.Dispose();
		}
		_luaQuickLoginCallback = cb;
		_shareSDK = instance;
        _shareSDK.qucikLoginHandler = (string accName, string pwd) =>
		{
			FUNCTAG_Login_CallShareFunc(accName, pwd);
		};
		//_shareSDK.QuickCall_ToLua_Login("112233", "2222");
	}
	public void FUNCTAG_Quick_Exit_call(ShareSDK instance, LuaFunction cb)
	{
		if (_luaQuickExitGameCallback != null)
		{
			_luaQuickExitGameCallback.Dispose();
		}
		_luaQuickExitGameCallback = cb;
		_shareSDK = instance;
		_shareSDK.qucikExitHandler = (string accName, string pwd) =>
		{
			FUNCTAG_Exit_CallShareFunc(accName, pwd);
		};
	}
	public void FUNCTAG_Quick_LoginOut_call(ShareSDK instance, LuaFunction cb)
	{
		if (_luaQuickLogoutCallback != null)
		{
			_luaQuickLogoutCallback.Dispose();
		}
		_luaQuickLogoutCallback = cb;
		_shareSDK = instance;
		_shareSDK.qucikLoginOutHandler = (string accName, string pwd) =>
		{
			FUNCTAG_LoginOut_CallShareFunc(accName, pwd);
		};
	}

	public void FUNCTAG_Quick_Switch_call(ShareSDK instance, LuaFunction cb)
	{
		if (_luaQuickSwitchAccountCallback != null)
		{
			_luaQuickSwitchAccountCallback.Dispose();
		}
		_luaQuickSwitchAccountCallback = cb;
		_shareSDK = instance;
		_shareSDK.qucikSwitchHandler = (string accName, string pwd) =>
		{
			FUNCTAG_Switch_CallShareFunc(accName, pwd);
		};
	}

	public void FUNCTAG_Quick_Cancellation_call(ShareSDK instance, LuaFunction cb)
	{
		if (_luaQuickCancellationAccountCallback != null)
		{
			_luaQuickCancellationAccountCallback.Dispose();
		}
		_luaQuickCancellationAccountCallback = cb;
		_shareSDK = instance;
		_shareSDK.qucikCancellationHandler = (string accName, string pwd) =>
		{
			FUNCTAG_Cancellation_CallShareFunc(accName, pwd);
		};
	}
}
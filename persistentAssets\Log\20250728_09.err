谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000046E41EDB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000046E41DC4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x0000000046F04715 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000006A3494C3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000006A3495D5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x0000000046F04E23 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x0000000046F04874 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x0000000046F04AA7 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000046E41EDB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000046E41DC4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x0000000046F04715 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000006A3494C3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000006A34931F (Mono JIT Code) [GoogleEventHandle.cs:134] GoogleEventHandle:GooglePayInit () 
0x000000006A348DD4 (Mono JIT Code) [ShareSDKManagerWrap.cs:85] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x00000000502A8653 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16DCF1F7 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000006A32883C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000006A3286BA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000006A338587 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000006A3383D0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000006A3375BC (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x000000006A33724E (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000C27012 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart


谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000046E41EDB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000046E41DC4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x0000000046F04715 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000006A3494C3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000006A34931F (Mono JIT Code) [GoogleEventHandle.cs:134] GoogleEventHandle:GooglePayInit () 
0x000000006A348DD4 (Mono JIT Code) [ShareSDKManagerWrap.cs:85] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x00000000502A8653 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FFF16D52A58 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16DCF1F7 ((<unknown>)) 
0x00007FFF16D724E0 ((<unknown>)) 
0x00007FFF16D87122 ((<unknown>)) 
0x000000006A32883C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000006A3286BA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000006A338587 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000006A3383D0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000006A3375BC (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x000000006A33724E (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000C27012 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF175664D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF174B8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFF750BE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFF7615C34C (ntdll) RtlUserThreadStart



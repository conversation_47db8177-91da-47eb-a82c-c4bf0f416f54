with:1185  hight:343
[logic/login/CLoginIcnTiShi.lua:2]:!!!!!!!!!!!!!!!!!CLoginIcnTiShi!!!!!!!!!!!!!!!!!
谷歌支付初始化失败
谷歌支付初始化失败
[logic/misc/CShareCtrl.lua:42]:jit    true    SSE3    SSE4.1    BMI2    fold    cse    dce    fwd    dse    narrow    loop    abc    sink    fuse
[logic/login/CLoginCtrl.lua:5]:公告初始化...
[logic/base/CHttpCtrl.lua:32]:http get ->    https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=rlU84dprH6I7sMjBkqM0gizl&client_secret=c15395c2433c4a904d22aa27eab8321b&
[logic/ui/CViewCtrl.lua:94]:CNotifyView ShowView
[logic/ui/CViewCtrl.lua:94]:CBottomView ShowView
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x7b251850"
|  json_result = true
|  timer = 16
}
[logic/audio/CSpeechCtrl.lua:72]:OnTokenGet err
[logic/base/CResCtrl.lua:199]:-->resource init done!!! 12
[logic/ui/CViewBase.lua:125]:CBottomView LoadDone!
[logic/ui/CViewBase.lua:125]:CNotifyView LoadDone!
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {}
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/login/CLoginIcnTiShi.lua:4]:~~~~~~~~~~~~CLoginIcnTiShi.ctor~~~~~~~~~~~~~~~~~~
[logic/base/CHttpCtrl.lua:32]:http get ->    http://************:88/Note/note2408.json
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x6ef76e30"
|  json_result = true
|  timer = 61
}
[core/table.lua:94]:cb tResult-> = {
|  info = {
|  |  role_list = {}
|  |  server_info = {
|  |  |  groups = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 1
|  |  |  |  |  name = "1-10区"
|  |  |  |  }
|  |  |  }
|  |  |  infoList = {
|  |  |  |  [1] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]服务器在8月16日22点00分进行不停机维护，解决ios无法使用可选御灵道具，0点掉线问题！[-]"
|  |  |  |  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "维护公告"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]欢迎来到《剑与火之歌（0.05折）》！[-]"
|  |  |  |  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "开服了"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]单日累充活动[-]"
|  |  |  |  |  |  |  |  title = "【活动类型】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [10] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*120[-]"
|  |  |  |  |  |  |  |  title = "单日充值1500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [11] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1[-]"
|  |  |  |  |  |  |  |  title = "单日充值2500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [12] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*5、万能碎片*500、原石盒子*20、附魔材料盒子*20[-]"
|  |  |  |  |  |  |  |  title = "单日充值5000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [13] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.每日均可参与，高档位可领所有低档位；
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "【活动时间】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]单日真实充值达到指定金额，即可领取该档位的奖励，本活动需要对应真实折后充值参与（高档位可以领取低档位的奖励)，每日活动重置[-]"
|  |  |  |  |  |  |  |  title = "【活动说明】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  |  |  |  title = "【领取方式】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "[ff0000]武器原石*5、星梦矿*10、金币*20000[-]"
|  |  |  |  |  |  |  |  title = "单日充值50元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*10、体力药水*1、鲜肉包*30、金币*100000[-]"
|  |  |  |  |  |  |  |  title = "单日充值150元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [7] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*20、体力药水*2、焦糖包*60、金币*200000[-]"
|  |  |  |  |  |  |  |  title = "单日充值250元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [8] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1[-]"
|  |  |  |  |  |  |  |  title = "单日充值500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [9] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3[-]"
|  |  |  |  |  |  |  |  title = "单日充值1000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "单日累充"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久累计充值活动[-]"
|  |  |  |  |  |  |  |  title = "活动名称："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [10] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*400、一发入魂碎片*100、原石盒子*40、附魔材料盒子*40、金币*3000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值7000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [11] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*700、一发入魂碎片*200、原石盒子*50、附魔材料盒子*50、金币*5000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值10000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [12] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.各档位奖励永久仅可获得一次返利，高档位可领所有低档位。
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额，活动解释权归游戏方所有。[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  |  |  |  title = "领取方式："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]活动为永久累计真实充值，高档位可领取低档位所有物品（每个档位奖励仅可领取一次）[-]"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "[ff0000]梦觉书·零*50、星梦矿*50、金币*10000[-]"
|  |  |  |  |  |  |  |  title = "累计充值300元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "[ff0000]2星精英伙伴*10、原石盒子*5、附魔材料盒子*5、金币*50000[-]"
|  |  |  |  |  |  |  |  title = "累计充值500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "[ff0000]2星精英伙伴*20、原石盒子*10、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  |  |  |  title = "累计充值1000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [7] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*1、原石盒子*30、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  |  |  |  title = "累计充值2000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [8] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*100、一发入魂碎片*100、原石盒子*20、附魔材料盒子*20、金币*1000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值3000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [9] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*200、一发入魂碎片*200、原石盒子*30、附魔材料盒子*30、金币*2000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值5000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "累计充值"
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]所有区服开服之日起3天内，第一个累计实际真实充值金额1000元的玩家，可获得该区服务器冠名权+3星传说伙伴任选*1（需提供具体道具名，伙伴包括：亦候、袁雀、白殊、莹月、奉主夜鹤、浮屠僧）[-]"
|  |  |  |  |  |  |  |  title = "服务器冠名活动："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.达成条件的玩家需向客服申请，核实后将于5个工作日内完成冠名及发放奖励至玩家邮箱；
2.资格获得者可以自定义自己所在区服的服务器名称，冠名后不可修改，请慎重取名；
3.更改后，该区服名称永久有效；
4.服务器命名规则：不超过4个汉字，不支持符号、英文，禁止色情反动、诋毁他人等不文明或破坏社会风气的词语。
后面满足条件的玩家无法获得冠名权限但新服前三名满足条件的玩家都可以联系客服领取到冠名奖励呦！

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "冠名活动"
|  |  |  |  }
|  |  |  }
|  |  |  ports = {}
|  |  |  role_list = ""
|  |  |  servers = {
|  |  |  |  [1] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 1
|  |  |  |  |  ip = "************"
|  |  |  |  |  name = "剑与火之歌"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1711501140
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10001"
|  |  |  |  |  start_time = 1711501140
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  }
|  |  }
|  |  token = ""
|  }
}
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 1
|  |  |  |  ip = "************"
|  |  |  |  name = "剑与火之歌"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1711501140
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 1
|  |  |  |  start_time = 1711501140
|  |  |  |  state = 1
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:设置公告: = {
|  [1] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]服务器在8月16日22点00分进行不停机维护，解决ios无法使用可选御灵道具，0点掉线问题！[-]"
|  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "维护公告"
|  }
|  [2] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]欢迎来到《剑与火之歌（0.05折）》！[-]"
|  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "开服了"
|  }
|  [3] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]单日累充活动[-]"
|  |  |  |  |  title = "【活动类型】"
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*120[-]"
|  |  |  |  |  title = "单日充值1500元："
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1[-]"
|  |  |  |  |  title = "单日充值2500元："
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*5、万能碎片*500、原石盒子*20、附魔材料盒子*20[-]"
|  |  |  |  |  title = "单日充值5000元："
|  |  |  |  }
|  |  |  |  [13] = {
|  |  |  |  |  text = "[ff0000]1.每日均可参与，高档位可领所有低档位；
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "【活动时间】"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]单日真实充值达到指定金额，即可领取该档位的奖励，本活动需要对应真实折后充值参与（高档位可以领取低档位的奖励)，每日活动重置[-]"
|  |  |  |  |  title = "【活动说明】"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  title = "【领取方式】"
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "[ff0000]武器原石*5、星梦矿*10、金币*20000[-]"
|  |  |  |  |  title = "单日充值50元："
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*10、体力药水*1、鲜肉包*30、金币*100000[-]"
|  |  |  |  |  title = "单日充值150元："
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*20、体力药水*2、焦糖包*60、金币*200000[-]"
|  |  |  |  |  title = "单日充值250元："
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  text = "[ff0000]装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1[-]"
|  |  |  |  |  title = "单日充值500元："
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  text = "[ff0000]装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3[-]"
|  |  |  |  |  title = "单日充值1000元："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "单日累充"
|  }
|  [4] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]永久累计充值活动[-]"
|  |  |  |  |  title = "活动名称："
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  text = "[ff0000]万能碎片*400、一发入魂碎片*100、原石盒子*40、附魔材料盒子*40、金币*3000000[-]"
|  |  |  |  |  title = "累计充值7000元："
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  text = "[ff0000]万能碎片*700、一发入魂碎片*200、原石盒子*50、附魔材料盒子*50、金币*5000000[-]"
|  |  |  |  |  title = "累计充值10000元："
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  text = "[ff0000]1.各档位奖励永久仅可获得一次返利，高档位可领所有低档位。
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额，活动解释权归游戏方所有。[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  title = "领取方式："
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]活动为永久累计真实充值，高档位可领取低档位所有物品（每个档位奖励仅可领取一次）[-]"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "[ff0000]梦觉书·零*50、星梦矿*50、金币*10000[-]"
|  |  |  |  |  title = "累计充值300元："
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "[ff0000]2星精英伙伴*10、原石盒子*5、附魔材料盒子*5、金币*50000[-]"
|  |  |  |  |  title = "累计充值500元："
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "[ff0000]2星精英伙伴*20、原石盒子*10、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  title = "累计充值1000元："
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*1、原石盒子*30、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  title = "累计充值2000元："
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  text = "[ff0000]万能碎片*100、一发入魂碎片*100、原石盒子*20、附魔材料盒子*20、金币*1000000[-]"
|  |  |  |  |  title = "累计充值3000元："
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  text = "[ff0000]万能碎片*200、一发入魂碎片*200、原石盒子*30、附魔材料盒子*30、金币*2000000[-]"
|  |  |  |  |  title = "累计充值5000元："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "累计充值"
|  }
|  [5] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]所有区服开服之日起3天内，第一个累计实际真实充值金额1000元的玩家，可获得该区服务器冠名权+3星传说伙伴任选*1（需提供具体道具名，伙伴包括：亦候、袁雀、白殊、莹月、奉主夜鹤、浮屠僧）[-]"
|  |  |  |  |  title = "服务器冠名活动："
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]1.达成条件的玩家需向客服申请，核实后将于5个工作日内完成冠名及发放奖励至玩家邮箱；
2.资格获得者可以自定义自己所在区服的服务器名称，冠名后不可修改，请慎重取名；
3.更改后，该区服名称永久有效；
4.服务器命名规则：不超过4个汉字，不支持符号、英文，禁止色情反动、诋毁他人等不文明或破坏社会风气的词语。
后面满足条件的玩家无法获得冠名权限但新服前三名满足条件的玩家都可以联系客服领取到冠名奖励呦！

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "冠名活动"
|  }
}
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://************:8081/common/accountList"
}
[core/global.lua:59]:<color=#ffeb04>CLoginServerPage.SetServer</color>
[logic/ui/CViewCtrl.lua:94]:CLoginNoticeView ShowView
[logic/ui/CViewBase.lua:125]:CLoginNoticeView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CLoginNoticeView     CloseView
[logic/login/CLoginCtrl.lua:154]:ConnectServer =     table:0x6EF7D140    table:0x6EE81C08
[net/CNetCtrl.lua:114]:Test连接    ************    7011
[core/table.lua:94]:-->Net Receive: login.GS2CHello = {
|  time = **********
}
[core/table.lua:94]:<--Net Send: login.C2GSQueryLogin = {
|  res_file_version = {}
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:48:03
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x6ee26968"
|  json_result = true
|  timer = 67
}
[net/netlogin.lua:210]:netlogin.GS2CQueryLogin-->
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 1001
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginAccount = {
|  account = "w8773610226"
|  client_svn_version = 96
|  client_version = "1.15.0.0"
|  device = "82RC (LENOVO)"
|  imei = ""
|  is_qrcode = 0
|  mac = "9C-2D-CD-1C-C7-E5"
|  os = "Windows 10  (10.0.0) 64bit"
|  platform = 3
|  udid = "d1bcbaffb9feae17994f24d0a8ec9d4d08cb3f95"
}
[core/global.lua:59]:<color=#ffeb04>更新代码: 1049</color>
[logic/base/Utils.lua:402]:local framever, dllver, resver = C_api.Utils.GetResVersion()
if resver >= 96 or Utils.IsEditor() then
	function CWelfareCtrl.IsOpenCostSave(self)
		local b = false
		local t = g_TimeCtrl:GetTimeS()	
		if self.m_CostSaveStartTime ~= 0 and self.m_CostSaveEndTime ~= 0 and t > self.m_CostSaveStartTime and t < self.m_CostSaveEndTime then
			b = true
		end
		return b
	end
	function CPartnerHireView.IsCanYFRH(self, iParID)
		local d = data.partnerhiredata.DATA
		if table.index(d[iParID]["recommand_list"], "一发入魂") then
			return self:IsCanSSRDraw()
		end
	end
	function CPartnerEquipPage.ChangePart(self)
		local oItem = g_ItemCtrl:GetItem(self.m_CurItemID)
		if oItem and oItem:GetValue("level") == define.Partner.ParEquip.MaxLevel and oItem:GetValue("star") < define.Partner.ParEquip.MaxStar then
			if self.m_CurPart ~= self.m_UpStarPart and self.m_CurPart ~= self.m_UpStonePart then
				self.m_UpStarBtn:SetSelected(true)
				self:SwitchPart()
			end
		end
	end
	data.netdata.BAN["proto"]["herobox"]["item"]["GS2CItemQuickUse"] = true
end

[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 1001
}
[core/table.lua:94]:-->Net Receive: login.GS2CCheckInviteCodeResult = {
|  msg = "邀请码验证已关闭"
|  result = 1
}
[core/table.lua:94]:-->Net Receive: login.GS2CLoginAccount = {
|  account = "w8773610226"
|  role_list = {
|  |  [1] = {
|  |  |  grade = 10
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 120
|  |  |  |  weapon = 2500
|  |  |  }
|  |  |  pid = 10008
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginRole = {
|  account = "w8773610226"
|  pid = 10008
}
[core/global.lua:59]:<color=#ffeb04>读取系统设置本地数据，帐号为  w8773610226</color>
[core/global.lua:59]:<color=#ffeb04>w8773610226 的系统设置本地数据为 nil，使用默认数据</color>
[core/table.lua:94]:-->Net Receive: login.GS2CLoginRole = {
|  account = "w8773610226"
|  pid = 10008
|  role = {
|  |  active = 6
|  |  bcmd = {
|  |  |  [1] = {
|  |  |  |  idx = 208
|  |  |  }
|  |  |  [10] = {
|  |  |  |  idx = 105
|  |  |  }
|  |  |  [11] = {
|  |  |  |  idx = 106
|  |  |  }
|  |  |  [12] = {
|  |  |  |  idx = 107
|  |  |  }
|  |  |  [13] = {
|  |  |  |  idx = 108
|  |  |  }
|  |  |  [14] = {
|  |  |  |  idx = 205
|  |  |  }
|  |  |  [15] = {
|  |  |  |  idx = 206
|  |  |  }
|  |  |  [16] = {
|  |  |  |  idx = 207
|  |  |  }
|  |  |  [2] = {
|  |  |  |  cmd = "减速"
|  |  |  |  idx = 203
|  |  |  }
|  |  |  [3] = {
|  |  |  |  cmd = "禁疗"
|  |  |  |  idx = 204
|  |  |  }
|  |  |  [4] = {
|  |  |  |  cmd = "集火"
|  |  |  |  idx = 201
|  |  |  }
|  |  |  [5] = {
|  |  |  |  cmd = "封印"
|  |  |  |  idx = 202
|  |  |  }
|  |  |  [6] = {
|  |  |  |  cmd = "治疗"
|  |  |  |  idx = 101
|  |  |  }
|  |  |  [7] = {
|  |  |  |  cmd = "复活"
|  |  |  |  idx = 102
|  |  |  }
|  |  |  [8] = {
|  |  |  |  cmd = "加速"
|  |  |  |  idx = 103
|  |  |  }
|  |  |  [9] = {
|  |  |  |  cmd = "解封"
|  |  |  |  idx = 104
|  |  |  }
|  |  }
|  |  coin = 59350
|  |  energy = 120
|  |  exp = 14610
|  |  grade = 10
|  |  kp_sdk_info = {
|  |  |  create_time = **********
|  |  |  upgrade_time = **********
|  |  }
|  |  mask = "7e6683d1ffffffe"
|  |  model_info = {
|  |  |  color = {
|  |  |  |  [1] = 0
|  |  |  }
|  |  |  shape = 120
|  |  |  weapon = 2500
|  |  }
|  |  name = "颔首之孵化者"
|  |  open_day = 61
|  |  org_fuben_cnt = 2
|  |  power = **********
|  |  school = 1
|  |  school_branch = 1
|  |  sex = 2
|  |  show_id = 10008
|  |  skill_point = 9
|  |  systemsetting = {}
|  }
|  role_token = "**************"
|  xg_account = "bus10008"
}
[logic/ui/CViewCtrl.lua:94]:CMainMenuView ShowView
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 0
|  active = 6
|  arenamedal = 0
|  attack = 0
|  bcmd = {
|  |  [1] = {
|  |  |  idx = 208
|  |  }
|  |  [10] = {
|  |  |  idx = 105
|  |  }
|  |  [11] = {
|  |  |  idx = 106
|  |  }
|  |  [12] = {
|  |  |  idx = 107
|  |  }
|  |  [13] = {
|  |  |  idx = 108
|  |  }
|  |  [14] = {
|  |  |  idx = 205
|  |  }
|  |  [15] = {
|  |  |  idx = 206
|  |  }
|  |  [16] = {
|  |  |  idx = 207
|  |  }
|  |  [2] = {
|  |  |  cmd = "减速"
|  |  |  idx = 203
|  |  }
|  |  [3] = {
|  |  |  cmd = "禁疗"
|  |  |  idx = 204
|  |  }
|  |  [4] = {
|  |  |  cmd = "集火"
|  |  |  idx = 201
|  |  }
|  |  [5] = {
|  |  |  cmd = "封印"
|  |  |  idx = 202
|  |  }
|  |  [6] = {
|  |  |  cmd = "治疗"
|  |  |  idx = 101
|  |  }
|  |  [7] = {
|  |  |  cmd = "复活"
|  |  |  idx = 102
|  |  }
|  |  [8] = {
|  |  |  cmd = "加速"
|  |  |  idx = 103
|  |  }
|  |  [9] = {
|  |  |  cmd = "解封"
|  |  |  idx = 104
|  |  }
|  }
|  camp = 0
|  chatself = false
|  chubeiexp = 0
|  coin = 59350
|  coin_over = 0
|  color_coin = 0
|  critical_damage = 0
|  critical_ratio = 0
|  cure_critical_ratio = 0
|  defense = 0
|  energy = 120
|  exp = 14610
|  followers = {}
|  goldcoin = 0
|  grade = 10
|  hp = 0
|  kp_sdk_info = {
|  |  create_time = **********
|  |  upgrade_time = **********
|  }
|  max_hp = 0
|  medal = 0
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 120
|  |  weapon = 2500
|  }
|  name = "颔首之孵化者"
|  open_day = 61
|  org_fuben_cnt = 2
|  org_offer = 0
|  power = **********
|  res_abnormal_ratio = 0
|  res_critical_ratio = 0
|  school = 1
|  school_branch = 1
|  sex = 2
|  show_id = 10008
|  skill_point = 9
|  skin = 0
|  speed = 0
|  systemsetting = {}
|  title_info = {}
|  trapmine_point = 0
|  travel_score = 0
|  upvote_amount = 0
}
[logic/misc/CTimeCtrl.lua:64]:HeartBeat-->开启心跳包检测
[core/table.lua:94]:<--Net Send: title.C2GSTitleInfoList = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGamePushSetting = {}
[core/table.lua:94]:-->Net Receive: player.GS2CShapeList = {
|  shapes = {
|  |  [1] = 120
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CGuidanceInfo = {
|  guidanceinfo = {
|  |  key = {
|  |  |  [1] = 7000041
|  |  |  [10] = 3002099
|  |  |  [11] = 2002099
|  |  |  [12] = 7000081
|  |  |  [13] = 2006099
|  |  |  [14] = 1003099
|  |  |  [15] = 3006099
|  |  |  [16] = 3007099
|  |  |  [17] = 3008099
|  |  |  [18] = 3003099
|  |  |  [19] = 7000091
|  |  |  [2] = 7000051
|  |  |  [20] = 3004099
|  |  |  [21] = 3005099
|  |  |  [22] = 5000300
|  |  |  [23] = 1004099
|  |  |  [24] = 3009099
|  |  |  [25] = 3010099
|  |  |  [26] = 3011099
|  |  |  [27] = 1006099
|  |  |  [28] = 3016099
|  |  |  [3] = 6010002
|  |  |  [4] = 1027099
|  |  |  [5] = 3001099
|  |  |  [6] = 1028099
|  |  |  [7] = 2001099
|  |  |  [8] = 7000061
|  |  |  [9] = 1001099
|  |  }
|  |  version = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>>>>>>>>>>>>>>>>>>>>> GS2CGuidanceInfo </color>
[core/table.lua:94]:-->Net Receive: travel.GS2CLoginTravelPartner = {
|  travel_partner = {
|  |  server_time = **********
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CServerGradeInfo = {
|  days = 16
|  server_grade = 95
}
[core/table.lua:94]:-->Net Receive: scene.GS2CShowScene = {
|  map_id = 101000
|  scene_id = 12
|  scene_name = "帝都"
}
[core/global.lua:59]:<color=#ffeb04>CResCtrl.MoveToSecondary</color>
[core/table.lua:94]:-->Net Receive: task.GS2CLoginTask = {
|  shimen_status = 1
|  taskdata = {
|  |  [1] = {
|  |  |  acceptnpc = 10310
|  |  |  clientnpc = {
|  |  |  |  [1] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 312
|  |  |  |  |  }
|  |  |  |  |  name = "伊露"
|  |  |  |  |  npcid = 27542
|  |  |  |  |  npctype = 10310
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 31000
|  |  |  |  |  |  y = 12000
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  detaildesc = "执行官伊露命令你把逃跑的嘟噜鸟抓回来！"
|  |  |  name = "调用"
|  |  |  patrolinfo = {}
|  |  |  pickiteminfo = {}
|  |  |  placeinfo = {
|  |  |  |  mapid = 101000
|  |  |  |  pos_x = 44
|  |  |  |  pos_y = 13
|  |  |  }
|  |  |  shapeinfo = {}
|  |  |  statusinfo = {
|  |  |  |  note = "CreateTask"
|  |  |  |  status = 2
|  |  |  }
|  |  |  submitnpc = 10310
|  |  |  target = 10310
|  |  |  targetdesc = "伊露的命令"
|  |  |  taskid = 10019
|  |  |  taskitem = {}
|  |  |  tasktype = 3
|  |  |  traceinfo = {}
|  |  |  type = 2
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>login task</color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x6ad807c0"
|  |  AssociatedPick = "function: 0x6ad80820"
|  |  AssociatedSubmit = "function: 0x6ad807f0"
|  |  CreateDefalutData = "function: 0x6ad7e7a0"
|  |  GetChaptetFubenData = "function: 0x6ad804e0"
|  |  GetProgressThing = "function: 0x6ad80880"
|  |  GetRemainTime = "function: 0x6ad7e738"
|  |  GetStatus = "function: 0x6ad808e0"
|  |  GetTaskClientExtStrDic = "function: 0x6ad80850"
|  |  GetTaskTypeSpriteteName = "function: 0x6ad804b0"
|  |  GetTraceInfo = "function: 0x6ad80608"
|  |  GetTraceNpcType = "function: 0x6ad80480"
|  |  GetValue = "function: 0x6ad80548"
|  |  IsAbandon = "function: 0x6ad805a8"
|  |  IsAddEscortDynamicNpc = "function: 0x6ad80c18"
|  |  IsMissMengTask = "function: 0x6ad7e768"
|  |  IsPassChaterFuben = "function: 0x6ad80510"
|  |  IsTaskSpecityAction = "function: 0x6ad805d8"
|  |  IsTaskSpecityCategory = "function: 0x6ad80790"
|  |  New = "function: 0x6ad85110"
|  |  NewByData = "function: 0x6ad7dce0"
|  |  RaiseProgressIdx = "function: 0x6ad808b0"
|  |  RefreshTask = "function: 0x6ad80578"
|  |  ResetEndTime = "function: 0x6ad7e808"
|  |  SetStatus = "function: 0x6ad80c48"
|  |  classname = "CTask"
|  |  ctor = "function: 0x6ad7e708"
|  }
|  m_CData = {
|  |  ChapterFb = "1,6"
|  |  autoDoNextTask = 10020
|  |  clientExtStr = ""
|  |  name = "调用"
|  |  submitNpcId = 10310
|  |  submitRewardStr = {
|  |  |  [1] = "R1019"
|  |  }
|  |  taskWalkingTips = "这小孩真有趣;没想到看走眼遭报应了;none"
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10310
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 312
|  |  |  |  }
|  |  |  |  name = "伊露"
|  |  |  |  npcid = 27542
|  |  |  |  npctype = 10310
|  |  |  |  pos_info = {
|  |  |  |  |  x = 31000
|  |  |  |  |  y = 12000
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "执行官伊露命令你把逃跑的嘟噜鸟抓回来！"
|  |  isdone = 0
|  |  name = "调用"
|  |  patrolinfo = {}
|  |  pickiteminfo = {}
|  |  placeinfo = {
|  |  |  mapid = 101000
|  |  |  pos_x = 44
|  |  |  pos_y = 13
|  |  }
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10310
|  |  target = 10310
|  |  targetdesc = "伊露的命令"
|  |  taskid = 10019
|  |  taskitem = {}
|  |  tasktype = 3
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CTeachProgress = {}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshPartnerTask = {
|  partnertask_progress = {
|  |  [1] = {
|  |  |  parid = 302
|  |  |  status = 1
|  |  |  taskid = 62080
|  |  }
|  |  [2] = {
|  |  |  parid = 502
|  |  |  status = 1
|  |  |  taskid = 62090
|  |  }
|  |  [3] = {
|  |  |  parid = 403
|  |  |  status = 1
|  |  |  taskid = 62169
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1016
|  |  [2] = 3005
|  }
}
[core/table.lua:94]:-->Net Receive: state.GS2CLoginState = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginHuodongInfo = {
|  convoyinfo = {
|  |  convoy_partner = 1201
|  |  free_time = 1
|  |  pool_info = {
|  |  |  [1] = {
|  |  |  |  level = 1
|  |  |  |  partnerid = 1505
|  |  |  |  pos = 1
|  |  |  |  rewardid = 1001
|  |  |  }
|  |  |  [2] = {
|  |  |  |  level = 2
|  |  |  |  partnerid = 1201
|  |  |  |  pos = 2
|  |  |  |  rewardid = 1002
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [3] = {
|  |  |  |  level = 3
|  |  |  |  partnerid = 1009
|  |  |  |  pos = 3
|  |  |  |  rewardid = 1003
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [4] = {
|  |  |  |  level = 4
|  |  |  |  partnerid = 1010
|  |  |  |  pos = 4
|  |  |  |  rewardid = 1004
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [5] = {
|  |  |  |  level = 5
|  |  |  |  partnerid = 1752
|  |  |  |  pos = 5
|  |  |  |  rewardid = 1005
|  |  |  |  weight = 25
|  |  |  }
|  |  }
|  |  selected_pos = 2
|  |  target_npc = 5014
|  }
|  dailytrain = {}
|  hireinfo = {
|  |  [1] = {
|  |  |  parid = 502
|  |  |  times = 1
|  |  }
|  |  [2] = {
|  |  |  parid = 403
|  |  |  times = 1
|  |  }
|  }
|  huntinfo = {}
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginChapterInfo = {
|  extrareward_info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 6
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = 1
|  |  |  level = 4
|  |  |  type = 1
|  |  }
|  }
|  finalchapter = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 6
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  type = 1
|  |  }
|  }
|  totalstar_info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  star = 18
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CUpdateConvoyInfo = {
|  convoyinfo = {
|  |  convoy_partner = 1201
|  |  free_time = 1
|  |  pool_info = {
|  |  |  [1] = {
|  |  |  |  level = 1
|  |  |  |  partnerid = 1504
|  |  |  |  pos = 1
|  |  |  |  rewardid = 1001
|  |  |  }
|  |  |  [2] = {
|  |  |  |  level = 2
|  |  |  |  partnerid = 1201
|  |  |  |  pos = 2
|  |  |  |  rewardid = 1002
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [3] = {
|  |  |  |  level = 3
|  |  |  |  partnerid = 1009
|  |  |  |  pos = 3
|  |  |  |  rewardid = 1003
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [4] = {
|  |  |  |  level = 4
|  |  |  |  partnerid = 1510
|  |  |  |  pos = 4
|  |  |  |  rewardid = 1004
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [5] = {
|  |  |  |  level = 5
|  |  |  |  partnerid = 1020
|  |  |  |  pos = 5
|  |  |  |  rewardid = 1005
|  |  |  |  weight = 25
|  |  |  }
|  |  }
|  |  selected_pos = 2
|  |  target_npc = 5015
|  }
}
[core/table.lua:94]:-->Net Receive: handbook.GS2CLoginBookList = {
|  book_list = {
|  |  [1] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315021
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115021
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100502
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313021
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  id = 313022
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113021
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100302
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [3] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 314031
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114031
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100403
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  }
|  red_points = {
|  |  [1] = {
|  |  |  book_type = 1
|  |  |  red_point = 1
|  |  }
|  |  [2] = {
|  |  |  book_type = 2
|  |  }
|  |  [3] = {
|  |  |  book_type = 3
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: team.GS2CInviteInfo = {}
[core/table.lua:94]:-->Net Receive: mail.GS2CLoginMail = {
|  simpleinfo = {
|  |  [1] = {
|  |  |  createtime = 1752549158
|  |  |  hasattach = 1
|  |  |  keeptime = 7776000
|  |  |  mailid = 1
|  |  |  subject = "喵小萌的来信"
|  |  |  title = "冲榜返利"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: friend.GS2CLoginFriend = {}
[core/global.lua:59]:<color=#ffeb04>好友版本一致，无需更新</color>
[core/table.lua:94]:-->Net Receive: friend.GS2CApplyList = {}
[core/table.lua:94]:-->Net Receive: friend.GS2CFriendSetting = {
|  setting = {
|  |  notify = 1
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CDrawCardUI = {}
[core/table.lua:94]:-->Net Receive: item.GS2CLoginItem = {
|  itemdata = {
|  |  [1] = {
|  |  |  amount = 3
|  |  |  create_time = 1752740705
|  |  |  id = 1
|  |  |  itemlevel = 2
|  |  |  name = "一星云母"
|  |  |  sid = 14031
|  |  }
|  |  [10] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 3
|  |  |  |  stone_sid = 4110000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 9
|  |  |  itemlevel = 1
|  |  |  name = "练习絮语衣"
|  |  |  power = 32
|  |  |  sid = 2320000
|  |  }
|  |  [11] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 4
|  |  |  |  stone_sid = 4200000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 12
|  |  |  itemlevel = 1
|  |  |  name = "练习草织戒"
|  |  |  power = 19
|  |  |  sid = 2400000
|  |  }
|  |  [12] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 364
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 72
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 5
|  |  |  |  stone_sid = 4310000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 10
|  |  |  itemlevel = 1
|  |  |  name = "练习离叶腰"
|  |  |  power = 72
|  |  |  sid = 2520000
|  |  }
|  |  [13] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "speed"
|  |  |  |  |  value = 33
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 11
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 77
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 6
|  |  |  |  stone_sid = 4410000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 13
|  |  |  itemlevel = 1
|  |  |  name = "练习青藤鞋"
|  |  |  power = 77
|  |  |  sid = 2620000
|  |  }
|  |  [2] = {
|  |  |  amount = 5
|  |  |  create_time = 1752738555
|  |  |  id = 2
|  |  |  itemlevel = 5
|  |  |  name = "扫荡券"
|  |  |  sid = 10030
|  |  }
|  |  [3] = {
|  |  |  amount = 1
|  |  |  create_time = 1752734064
|  |  |  id = 3
|  |  |  itemlevel = 2
|  |  |  name = "3级符石礼包"
|  |  |  sid = 14103
|  |  }
|  |  [4] = {
|  |  |  amount = 2
|  |  |  create_time = 1752740705
|  |  |  id = 4
|  |  |  itemlevel = 2
|  |  |  name = "深蓝琥珀"
|  |  |  sid = 14021
|  |  }
|  |  [5] = {
|  |  |  amount = 1
|  |  |  create_time = 1752738555
|  |  |  id = 5
|  |  |  itemlevel = 2
|  |  |  name = "3级绯红宝石"
|  |  |  sid = 18002
|  |  }
|  |  [6] = {
|  |  |  amount = 1
|  |  |  create_time = 1751883929
|  |  |  id = 6
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 1
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101005
|  |  }
|  |  [7] = {
|  |  |  amount = 1
|  |  |  create_time = 1752134839
|  |  |  id = 7
|  |  |  itemlevel = 3
|  |  |  name = "梦觉书·低"
|  |  |  sid = 27402
|  |  }
|  |  [8] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 1
|  |  |  |  stone_sid = 3100000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 8
|  |  |  itemlevel = 1
|  |  |  name = "练习红魔钺"
|  |  |  power = 57
|  |  |  sid = 2100000
|  |  }
|  |  [9] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 121
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 24
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 2
|  |  |  |  stone_sid = 4000000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 11
|  |  |  itemlevel = 1
|  |  |  name = "练习飘逸巾"
|  |  |  power = 24
|  |  |  sid = 2200000
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>loginitem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcFightInfoList = {
|  info_list = {
|  |  [1] = {
|  |  |  npc_type = 10024
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [10] = {
|  |  |  npc_type = 10007
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [11] = {
|  |  |  npc_type = 10008
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [12] = {
|  |  |  npc_type = 10009
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [13] = {
|  |  |  npc_type = 10011
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [14] = {
|  |  |  npc_type = 10012
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [15] = {
|  |  |  npc_type = 10013
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [16] = {
|  |  |  npc_type = 10000
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [17] = {
|  |  |  npc_type = 10023
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [18] = {
|  |  |  npc_type = 10014
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [19] = {
|  |  |  npc_type = 10015
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [2] = {
|  |  |  npc_type = 10002
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [20] = {
|  |  |  npc_type = 10016
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [21] = {
|  |  |  npc_type = 10017
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [22] = {
|  |  |  npc_type = 10018
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [23] = {
|  |  |  npc_type = 10019
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [24] = {
|  |  |  npc_type = 10020
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [25] = {
|  |  |  npc_type = 10021
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [26] = {
|  |  |  npc_type = 10022
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [3] = {
|  |  |  npc_type = 10010
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [4] = {
|  |  |  npc_type = 10001
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [5] = {
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [6] = {
|  |  |  npc_type = 10003
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [7] = {
|  |  |  npc_type = 10004
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [8] = {
|  |  |  npc_type = 10005
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [9] = {
|  |  |  npc_type = 10006
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CRefreshRewardBack = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 51117
|  |  |  type = "zsk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 51117
|  |  |  type = "yk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
|  mask = "c"
}
[core/table.lua:94]:CNetCtrl解析mask: GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 51117
|  |  |  type = "zsk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 51117
|  |  |  type = "yk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: store.GS2CPayForColorCoinInfo = {
|  colorcoin_list = {
|  |  [1] = {
|  |  |  key = "goldcoinstore_1012"
|  |  }
|  |  [10] = {
|  |  |  key = "goldcoinstore_2011"
|  |  }
|  |  [11] = {
|  |  |  key = "goldcoinstore_2012"
|  |  }
|  |  [12] = {
|  |  |  key = "goldcoinstore_2013"
|  |  }
|  |  [13] = {
|  |  |  key = "goldcoinstore_2014"
|  |  }
|  |  [14] = {
|  |  |  key = "goldcoinstore_2015"
|  |  }
|  |  [15] = {
|  |  |  key = "goldcoinstore_1015"
|  |  }
|  |  [16] = {
|  |  |  key = "goldcoinstore_1005"
|  |  }
|  |  [17] = {
|  |  |  key = "goldcoinstore_1006"
|  |  }
|  |  [18] = {
|  |  |  key = "goldcoinstore_1007"
|  |  }
|  |  [19] = {
|  |  |  key = "goldcoinstore_1008"
|  |  }
|  |  [2] = {
|  |  |  key = "goldcoinstore_1013"
|  |  }
|  |  [20] = {
|  |  |  key = "goldcoinstore_1009"
|  |  }
|  |  [21] = {
|  |  |  key = "goldcoinstore_1010"
|  |  }
|  |  [22] = {
|  |  |  key = "goldcoinstore_1011"
|  |  }
|  |  [3] = {
|  |  |  key = "goldcoinstore_1014"
|  |  }
|  |  [4] = {
|  |  |  key = "goldcoinstore_2005"
|  |  }
|  |  [5] = {
|  |  |  key = "goldcoinstore_2006"
|  |  }
|  |  [6] = {
|  |  |  key = "goldcoinstore_2007"
|  |  }
|  |  [7] = {
|  |  |  key = "goldcoinstore_2008"
|  |  }
|  |  [8] = {
|  |  |  key = "goldcoinstore_2009"
|  |  }
|  |  [9] = {
|  |  |  key = "goldcoinstore_2010"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeRewrad = {
|  end_time = 1751813939
|  start_time = 1751727600
}
[core/table.lua:94]:-->Net Receive: title.GS2CRemoveTitles = {
|  tidlist = {
|  |  [1] = 1001
|  |  [2] = 1002
|  |  [3] = 1003
|  |  [4] = 1004
|  |  [5] = 1005
|  |  [6] = 1006
|  |  [7] = 1007
|  |  [8] = 1008
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "8段"
|  |  tid = 1008
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "2段"
|  |  tid = 1002
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "3段"
|  |  tid = 1003
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "4段"
|  |  tid = 1004
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "5段"
|  |  tid = 1005
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "6段"
|  |  tid = 1006
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterScene = {
|  eid = 32
|  pos_info = {
|  |  face_y = 119705
|  |  x = 35030
|  |  y = 12163
|  }
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04>result  nil</color>
[core/global.lua:59]:<color=#ffeb04>=00000=  userdata: 0x8887e7c8 nil</color>
[logic/ui/CSprite.lua:159]:DynamicSprite dSprInfo    Item    10012
[logic/ui/CViewBase.lua:125]:CMainMenuView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/global.lua:59]:<color=#ffeb04>读取地图： 1020</color>
[core/global.lua:59]:<color=#ffeb04>Unity2017.4.30f1 localScale</color>
[logic/ui/CViewCtrl.lua:104]:CLoginView     CloseView
[logic/base/CResCtrl.lua:599]:res gc step start!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/global.lua:59]:<color=#ffeb04>地图加载完成: 1020 ,当前地图: nil</color>
[core/table.lua:94]:Table = {}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "7段"
|  |  tid = 1007
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginTrapmine = {
|  rare_monster = {
|  |  [1] = {
|  |  |  map_id = 202000
|  |  |  partypes = {
|  |  |  |  [1] = 312
|  |  |  |  [2] = 313
|  |  |  |  [3] = 316
|  |  |  }
|  |  }
|  |  [2] = {
|  |  |  map_id = 204000
|  |  |  partypes = {
|  |  |  |  [1] = 404
|  |  |  |  [2] = 402
|  |  |  |  [3] = 407
|  |  |  }
|  |  }
|  |  [3] = {
|  |  |  map_id = 205000
|  |  |  partypes = {
|  |  |  |  [1] = 508
|  |  |  |  [2] = 513
|  |  |  |  [3] = 509
|  |  |  }
|  |  }
|  |  [4] = {
|  |  |  map_id = 206000
|  |  |  partypes = {
|  |  |  |  [1] = 410
|  |  |  |  [2] = 414
|  |  |  |  [3] = 415
|  |  |  }
|  |  }
|  |  [5] = {
|  |  |  map_id = 210400
|  |  |  partypes = {
|  |  |  |  [1] = 504
|  |  |  |  [2] = 506
|  |  |  |  [3] = 507
|  |  |  }
|  |  }
|  |  [6] = {
|  |  |  map_id = 200000
|  |  |  partypes = {
|  |  |  |  [1] = 416
|  |  |  |  [2] = 417
|  |  |  |  [3] = 502
|  |  |  }
|  |  }
|  |  [7] = {
|  |  |  map_id = 201000
|  |  |  partypes = {
|  |  |  |  [1] = 308
|  |  |  |  [2] = 301
|  |  |  |  [3] = 302
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliReddot = {
|  cday = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CNewFieldBoss = {
|  bossid = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraWarState = {
|  time = 1753871152
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraQueueStatus = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CDailySignInfo = {
|  sign_info = {
|  |  [1] = {
|  |  |  key = "week"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COnlineGift = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHeroBoxRecord = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeScore = {
|  cur_id = 1
|  end_time = 1752505139
|  score_info = {}
|  start_time = 1751209200
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayBuy = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CHistoryCharge = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFirstChargeUI = {
|  bOpen = true
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliPoint = {
|  point = 4
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawCnt = {
|  cnt = 6
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliTime = {
|  endtime = 1752505139
|  starttime = 1751209200
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartnerList = {
|  partner_list = {
|  |  [1] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 461
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 93
|  |  |  equip_list = {
|  |  |  |  [1] = 6
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 9870
|  |  |  grade = 10
|  |  |  hp = 3120
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 3120
|  |  |  model_info = {
|  |  |  |  shape = 302
|  |  |  |  skin = 203020
|  |  |  }
|  |  |  name = "重华"
|  |  |  parid = 1
|  |  |  partner_type = 302
|  |  |  patahp = 3120
|  |  |  power = 1104
|  |  |  power_rank = 2
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30203
|  |  |  |  }
|  |  |  }
|  |  |  speed = 420
|  |  |  star = 1
|  |  }
|  |  [2] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 338
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 75
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 5050
|  |  |  grade = 6
|  |  |  hp = 2475
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 2475
|  |  |  model_info = {
|  |  |  |  shape = 502
|  |  |  |  skin = 205020
|  |  |  }
|  |  |  name = "马面面"
|  |  |  parid = 2
|  |  |  partner_type = 502
|  |  |  patahp = 2475
|  |  |  power = 840
|  |  |  power_rank = 4
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 500
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50203
|  |  |  |  }
|  |  |  }
|  |  |  speed = 315
|  |  |  star = 1
|  |  }
|  |  [3] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 316
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 600
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 70
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 2470
|  |  |  grade = 4
|  |  |  hp = 2441
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 2441
|  |  |  model_info = {
|  |  |  |  shape = 403
|  |  |  |  skin = 204030
|  |  |  }
|  |  |  name = "蛇姬"
|  |  |  parid = 3
|  |  |  partner_type = 403
|  |  |  patahp = 2441
|  |  |  power = 798
|  |  |  power_rank = 2
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40301
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40302
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40303
|  |  |  |  }
|  |  |  }
|  |  |  speed = 735
|  |  |  star = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CTodayInfo = {
|  info = {
|  |  mask = "0"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: todyInfo = {}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartner = {
|  fight_info = {
|  |  [1] = {
|  |  |  parid = 1
|  |  |  pos = 1
|  |  }
|  |  [2] = {
|  |  |  parid = 2
|  |  |  pos = 2
|  |  }
|  |  [3] = {
|  |  |  parid = 3
|  |  |  pos = 3
|  |  }
|  }
|  owned_equip_list = {
|  |  [1] = 6101001
|  }
|  owned_partner_list = {
|  |  [1] = 302
|  |  [2] = 502
|  |  [3] = 403
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CGameShare = {
|  game_share = {
|  |  [1] = {
|  |  |  type = "draw_card_share"
|  |  }
|  |  [2] = {
|  |  |  type = "skin_share"
|  |  }
|  |  [3] = {
|  |  |  type = "picture_share"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPicturePosList = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardInfo = {
|  breed_val = 6
|  login_day = 7
|  rewarded_day = 32
}
[logic/ui/CViewCtrl.lua:94]:CLoginRewardView ShowView
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawCnt = {
|  cnt = 7
}
[core/table.lua:94]:-->Net Receive: skill.GS2CLoginSkill = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3001
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3002
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  needcost = 15
|  |  |  sk = 3004
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  needcost = 25
|  |  |  sk = 3005
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3006
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3007
|  |  |  type = 1
|  |  }
|  }
}
[logic/skill/CSkillCtrl.lua:112]:技能数据登陆协议返回:
[core/table.lua:94]:Table = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3001
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3002
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  needcost = 15
|  |  |  sk = 3004
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  needcost = 25
|  |  |  sk = 3005
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3006
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3007
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginParSoulPlan = {}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  mask = "1997c2e0000000"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  give_org_equip = {}
|  give_org_wish = {}
|  is_equip_wish = 0
|  is_org_wish = 0
|  org_build_status = 0
|  org_build_time = 0
|  org_id = 0
|  org_leader = ""
|  org_level = 0
|  org_pos = 0
|  org_red_packet = 0
|  org_sign_reward = 0
|  org_status = 0
|  orgname = ""
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  mask = "10000000"
|  |  power_rank = 2
|  }
|  partnerid = 2
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  power_rank = 2
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  abnormal_attr_ratio = 540
|  |  attack = 310
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  mask = "87ff00"
|  |  max_hp = 2846
|  |  power = 892
|  |  res_abnormal_ratio = 540
|  |  res_critical_ratio = 500
|  |  speed = 753
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 540
|  attack = 310
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  hp = 0
|  max_hp = 2846
|  power = 892
|  res_abnormal_ratio = 540
|  res_critical_ratio = 500
|  speed = 753
}
[core/table.lua:94]:-->Net Receive: house.GS2CEnterHouse = {
|  buff_info = {}
|  furniture_info = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  lock_status = 1
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  type = 2
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  type = 3
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  type = 4
|  |  }
|  |  [5] = {
|  |  |  level = 1
|  |  |  type = 5
|  |  }
|  }
|  handle_type = 1
|  max_train = 1
|  max_warm_degree = 1000
|  owner_pid = 10008
|  partner_info = {
|  |  [1] = {
|  |  |  coin = 2900
|  |  |  type = 1001
|  |  }
|  }
|  warm_degree = 152
}
[core/table.lua:94]:-->Net Receive: house.GS2CPartnerExchangeUI = {
|  handle_type = 1
|  love_cnt = 10
|  max_gift_cnt = 10
|  max_love_cnt = 10
|  partner_gift_cnt = 10
}
[core/table.lua:94]:-->Net Receive: house.GS2COpenWorkDesk = {
|  desk_info = {
|  |  [1] = {
|  |  |  lock_status = 1
|  |  |  pos = 1
|  |  |  status = 1
|  |  }
|  |  [2] = {
|  |  |  pos = 2
|  |  |  status = 1
|  |  }
|  |  [3] = {
|  |  |  pos = 3
|  |  |  status = 1
|  |  }
|  |  [4] = {
|  |  |  lock_status = 1
|  |  |  pos = 4
|  |  |  status = 1
|  |  }
|  }
|  handle_type = 1
|  owner_pid = 10008
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 1
|  |  |  }
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  blist = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  id = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayMain = {
|  server_day = 21
}
[core/table.lua:94]:-->Net Receive: task.GS2CLoginAchieveTask = {
|  info = {
|  |  [1] = {
|  |  |  achievetype = 1
|  |  |  degree = 1
|  |  |  describe = "领取1次在线奖励"
|  |  |  name = "在线奖励"
|  |  |  target = 1
|  |  |  taskid = 31001
|  |  }
|  |  [2] = {
|  |  |  achievetype = 2
|  |  |  degree = 1
|  |  |  describe = "穿戴两件符文"
|  |  |  name = "穿戴符文（2）"
|  |  |  target = 2
|  |  |  taskid = 31533
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>LoginAchieveTask</color>
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1102
|  |  |  }
|  |  |  name = "峰馆长"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 2
|  |  npctype = 5040
|  }
|  eid = 1
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 1
|  pos_info = {
|  |  x = 31800
|  |  y = 17600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1102
|  }
|  name = "峰馆长"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1508
|  |  |  }
|  |  |  name = "小叶"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 5
|  |  npctype = 5041
|  }
|  eid = 2
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 2
|  pos_info = {
|  |  x = 40000
|  |  y = 14600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1508
|  }
|  name = "小叶"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小佳"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 11
|  |  npctype = 5043
|  }
|  eid = 4
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 4
|  pos_info = {
|  |  x = 27000
|  |  y = 4000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小佳"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小兰"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 14
|  |  npctype = 5044
|  }
|  eid = 5
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 5
|  pos_info = {
|  |  x = 19000
|  |  y = 4000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小兰"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1503
|  |  |  }
|  |  |  name = "喵小布"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 21
|  |  npctype = 5047
|  }
|  eid = 6
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 6
|  pos_info = {
|  |  x = 33000
|  |  y = 9500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1503
|  }
|  name = "喵小布"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1012
|  |  |  }
|  |  |  name = "克瑞斯汀"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 24
|  |  npctype = 5048
|  }
|  eid = 7
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 7
|  pos_info = {
|  |  x = 20200
|  |  y = 21100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1012
|  }
|  name = "克瑞斯汀"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小佳"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 57
|  |  npctype = 5064
|  }
|  eid = 8
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 8
|  pos_info = {
|  |  x = 13800
|  |  y = 20000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小佳"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1014
|  |  |  }
|  |  |  name = "乔焱"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 66
|  |  npctype = 5003
|  }
|  eid = 11
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 11
|  pos_info = {
|  |  x = 13200
|  |  y = 24600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1014
|  }
|  name = "乔焱"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1011
|  |  |  }
|  |  |  name = "遥"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 72
|  |  npctype = 5005
|  }
|  eid = 13
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 13
|  pos_info = {
|  |  x = 45500
|  |  y = 20000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1011
|  }
|  name = "遥"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 414
|  |  |  }
|  |  |  name = "蓝堂昼"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 75
|  |  npctype = 5006
|  }
|  eid = 14
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 14
|  pos_info = {
|  |  x = 28000
|  |  y = 14500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 414
|  }
|  name = "蓝堂昼"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 311
|  |  |  }
|  |  |  name = "花常酒"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 78
|  |  npctype = 5007
|  }
|  eid = 15
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 15
|  pos_info = {
|  |  x = 35600
|  |  y = 13400
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 311
|  }
|  name = "花常酒"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 508
|  |  |  }
|  |  |  name = "魂夕"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 81
|  |  npctype = 5008
|  }
|  eid = 16
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 16
|  pos_info = {
|  |  x = 24000
|  |  y = 12900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 508
|  }
|  name = "魂夕"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 417
|  |  |  }
|  |  |  name = "松姑子"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 220
|  |  npctype = 5019
|  }
|  eid = 21
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 21
|  pos_info = {
|  |  x = 42300
|  |  y = 24600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 417
|  }
|  name = "松姑子"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1016
|  |  |  }
|  |  |  name = "水生"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 196
|  |  npctype = 5009
|  }
|  eid = 17
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 17
|  pos_info = {
|  |  x = 24000
|  |  y = 22000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1016
|  }
|  name = "水生"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1150
|  |  |  }
|  |  |  name = "飞龙哥"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 199
|  |  npctype = 5010
|  }
|  eid = 18
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 18
|  pos_info = {
|  |  x = 29000
|  |  y = 3500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1150
|  }
|  name = "飞龙哥"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1151
|  |  |  }
|  |  |  name = "邓酒爷"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 202
|  |  npctype = 5011
|  }
|  eid = 19
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 19
|  pos_info = {
|  |  x = 22300
|  |  y = 5100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1151
|  }
|  name = "邓酒爷"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: title.GS2CTitleInfoList = {
|  infos = {
|  |  [1] = {
|  |  |  name = "8段"
|  |  |  tid = 1008
|  |  }
|  |  [10] = {
|  |  |  name = "应用宝贵族"
|  |  |  tid = 1017
|  |  }
|  |  [11] = {
|  |  |  name = "王者公会"
|  |  |  tid = 1018
|  |  }
|  |  [12] = {
|  |  |  name = "王者公会会长"
|  |  |  tid = 1019
|  |  }
|  |  [13] = {
|  |  |  name = "黄金公会"
|  |  |  tid = 1020
|  |  }
|  |  [14] = {
|  |  |  name = "黄金公会会长"
|  |  |  tid = 1021
|  |  }
|  |  [15] = {
|  |  |  name = "白银公会"
|  |  |  tid = 1022
|  |  }
|  |  [16] = {
|  |  |  name = "白银公会会长"
|  |  |  tid = 1023
|  |  }
|  |  [17] = {
|  |  |  name = "明日之星"
|  |  |  tid = 1024
|  |  }
|  |  [18] = {
|  |  |  name = "独孤求败"
|  |  |  tid = 1025
|  |  }
|  |  [19] = {
|  |  |  name = "谁与争锋"
|  |  |  tid = 1026
|  |  }
|  |  [2] = {
|  |  |  name = "测-贵族宝"
|  |  |  tid = 1009
|  |  }
|  |  [20] = {
|  |  |  name = "所向披靡"
|  |  |  tid = 1027
|  |  }
|  |  [21] = {
|  |  |  name = "地牢终结者"
|  |  |  tid = 1028
|  |  }
|  |  [22] = {
|  |  |  name = "地牢征服者"
|  |  |  tid = 1029
|  |  }
|  |  [23] = {
|  |  |  name = "地牢破坏者"
|  |  |  tid = 1030
|  |  }
|  |  [24] = {
|  |  |  name = "眯眯眼是变态"
|  |  |  tid = 1031
|  |  }
|  |  [25] = {
|  |  |  name = "邻家妹妹"
|  |  |  tid = 1032
|  |  }
|  |  [26] = {
|  |  |  name = "兄控"
|  |  |  tid = 1033
|  |  }
|  |  [27] = {
|  |  |  name = "农夫三拳"
|  |  |  tid = 1034
|  |  }
|  |  [28] = {
|  |  |  name = "赌神"
|  |  |  tid = 1035
|  |  }
|  |  [29] = {
|  |  |  name = "神之右手"
|  |  |  tid = 1036
|  |  }
|  |  [3] = {
|  |  |  name = "测-我就是托"
|  |  |  tid = 1010
|  |  }
|  |  [30] = {
|  |  |  name = "茶会噩梦"
|  |  |  tid = 1037
|  |  }
|  |  [31] = {
|  |  |  name = "我是傲娇"
|  |  |  tid = 1038
|  |  }
|  |  [32] = {
|  |  |  name = "闷骚美男子"
|  |  |  tid = 1039
|  |  }
|  |  [33] = {
|  |  |  name = "脸炮"
|  |  |  tid = 1040
|  |  }
|  |  [34] = {
|  |  |  name = "妹控"
|  |  |  tid = 1041
|  |  }
|  |  [35] = {
|  |  |  name = "守夜人"
|  |  |  tid = 1042
|  |  }
|  |  [36] = {
|  |  |  name = "泡泡娘"
|  |  |  tid = 1043
|  |  }
|  |  [37] = {
|  |  |  name = "姐控"
|  |  |  tid = 1044
|  |  }
|  |  [38] = {
|  |  |  name = "迷妹"
|  |  |  tid = 1045
|  |  }
|  |  [39] = {
|  |  |  name = "小天使"
|  |  |  tid = 1046
|  |  }
|  |  [4] = {
|  |  |  name = "测-粗壮大腿"
|  |  |  tid = 1011
|  |  }
|  |  [40] = {
|  |  |  name = "欺诈兔子"
|  |  |  tid = 1047
|  |  }
|  |  [41] = {
|  |  |  name = "天然呆"
|  |  |  tid = 1048
|  |  }
|  |  [42] = {
|  |  |  name = "开局一条狗"
|  |  |  tid = 1049
|  |  }
|  |  [43] = {
|  |  |  name = "种竹专业户"
|  |  |  tid = 1050
|  |  }
|  |  [44] = {
|  |  |  name = "顾家好男人"
|  |  |  tid = 1051
|  |  }
|  |  [45] = {
|  |  |  name = "无敌小钢炮"
|  |  |  tid = 1052
|  |  }
|  |  [46] = {
|  |  |  name = "小豆丁"
|  |  |  tid = 1053
|  |  }
|  |  [47] = {
|  |  |  name = "浪里白条"
|  |  |  tid = 1054
|  |  }
|  |  [48] = {
|  |  |  name = "话唠"
|  |  |  tid = 1055
|  |  }
|  |  [49] = {
|  |  |  name = "角落种蘑菇"
|  |  |  tid = 1056
|  |  }
|  |  [5] = {
|  |  |  name = "攻防战第一公会"
|  |  |  tid = 1012
|  |  }
|  |  [50] = {
|  |  |  name = "天然疯"
|  |  |  tid = 1057
|  |  }
|  |  [51] = {
|  |  |  name = "吃货"
|  |  |  tid = 1058
|  |  }
|  |  [52] = {
|  |  |  name = "控制狂"
|  |  |  tid = 1059
|  |  }
|  |  [53] = {
|  |  |  name = "女王大人"
|  |  |  tid = 1060
|  |  }
|  |  [54] = {
|  |  |  name = "八卦小王子"
|  |  |  tid = 1061
|  |  }
|  |  [55] = {
|  |  |  name = "三无"
|  |  |  tid = 1062
|  |  }
|  |  [56] = {
|  |  |  name = "养得累"
|  |  |  tid = 1063
|  |  }
|  |  [57] = {
|  |  |  name = "扑街之王"
|  |  |  tid = 1064
|  |  }
|  |  [58] = {
|  |  |  name = "抖S"
|  |  |  tid = 1065
|  |  }
|  |  [59] = {
|  |  |  name = "工作狂人"
|  |  |  tid = 1066
|  |  }
|  |  [6] = {
|  |  |  name = "攻防战第一司令"
|  |  |  tid = 1013
|  |  }
|  |  [60] = {
|  |  |  name = "精分中毒患者"
|  |  |  tid = 1067
|  |  }
|  |  [61] = {
|  |  |  name = "神神叨叨"
|  |  |  tid = 1068
|  |  }
|  |  [62] = {
|  |  |  name = "浮夸之神"
|  |  |  tid = 1069
|  |  }
|  |  [63] = {
|  |  |  name = "抖M"
|  |  |  tid = 1070
|  |  }
|  |  [64] = {
|  |  |  name = "自恋狂"
|  |  |  tid = 1071
|  |  }
|  |  [65] = {
|  |  |  name = "天然黑"
|  |  |  tid = 1072
|  |  }
|  |  [66] = {
|  |  |  name = "为所欲为"
|  |  |  tid = 1073
|  |  }
|  |  [67] = {
|  |  |  name = "氪金大佬"
|  |  |  tid = 1074
|  |  }
|  |  [68] = {
|  |  |  name = "应用宝帝王"
|  |  |  tid = 1075
|  |  }
|  |  [69] = {
|  |  |  name = "应用宝至尊"
|  |  |  tid = 1076
|  |  }
|  |  [7] = {
|  |  |  name = "梦魇之主"
|  |  |  tid = 1014
|  |  }
|  |  [70] = {
|  |  |  name = "公会新人"
|  |  |  tid = 1077
|  |  }
|  |  [71] = {
|  |  |  name = "公会成员"
|  |  |  tid = 1078
|  |  }
|  |  [72] = {
|  |  |  name = "公会精英"
|  |  |  tid = 1079
|  |  }
|  |  [73] = {
|  |  |  name = "公会副会"
|  |  |  tid = 1080
|  |  }
|  |  [74] = {
|  |  |  name = "公会会长"
|  |  |  tid = 1081
|  |  }
|  |  [75] = {
|  |  |  name = "情侣专属称谓"
|  |  |  tid = 1082
|  |  }
|  |  [76] = {
|  |  |  name = "命运之子"
|  |  |  tid = 1083
|  |  }
|  |  [77] = {
|  |  |  name = "不做大多数"
|  |  |  tid = 1084
|  |  }
|  |  [78] = {
|  |  |  name = "1段"
|  |  |  tid = 1001
|  |  }
|  |  [79] = {
|  |  |  name = "2段"
|  |  |  tid = 1002
|  |  }
|  |  [8] = {
|  |  |  name = "梦魇骑士"
|  |  |  tid = 1015
|  |  }
|  |  [80] = {
|  |  |  name = "3段"
|  |  |  tid = 1003
|  |  }
|  |  [81] = {
|  |  |  name = "4段"
|  |  |  tid = 1004
|  |  }
|  |  [82] = {
|  |  |  name = "5段"
|  |  |  tid = 1005
|  |  }
|  |  [83] = {
|  |  |  name = "6段"
|  |  |  tid = 1006
|  |  }
|  |  [84] = {
|  |  |  name = "7段"
|  |  |  tid = 1007
|  |  }
|  |  [9] = {
|  |  |  name = "梦魇猎人"
|  |  |  tid = 1016
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667284
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:48:04
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10115</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小叶 1509 [40,14.6,0] 0 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [45.8,10.2,0] 131</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  诚哥 1150 [47,9.4,0] -75 false</color>
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10107</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  喵小布 1503 [32,1.3,0] -6 false</color>
[logic/ui/CViewBase.lua:125]:CLoginRewardView LoadDone!
[logic/platform/CSdkCtrl.lua:393]:sdk upload: not init
[core/table.lua:94]:Table = {
|  [1] = "16"
|  [2] = "20"
}
[core/global.lua:59]:<color=#ffeb04>10 16 20</color>
[core/table.lua:94]:Table = {
|  [1] = "21"
|  [2] = "24"
}
[core/global.lua:59]:<color=#ffeb04>10 21 24</color>
[core/table.lua:94]:Table = {
|  [1] = "25"
|  [2] = "27"
}
[core/global.lua:59]:<color=#ffeb04>10 25 27</color>
[logic/base/CResCtrl.lua:626]:res gc finish!
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [34.2,9,0] 360</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 23026
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGetLoginReward = {
|  day = 4
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 23026
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardDay = {
|  rewarded_day = 40
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  sid = 1003
|  |  |  virtual = 1025
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 叫我出来有事？</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 23026
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGetLoginReward = {
|  day = 7
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 23026
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardDay = {
|  rewarded_day = 104
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CShowLoginRewardUI = {
|  next_day = 8
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得 #O[万能碎片] x 5#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 5
|  |  create_time = 1753667289
|  |  id = 14
|  |  itemlevel = 4
|  |  name = "万能碎片"
|  |  sid = 14002
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 5
|  |  |  sid = 14002
|  |  |  virtual = 14002
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 喵~喵呜喵~</color>
[logic/ui/CViewCtrl.lua:104]:CLoginRewardView     CloseView
[logic/ui/CViewCtrl.lua:94]:CLoginRewardNextView ShowView
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewBase.lua:125]:CLoginRewardNextView LoadDone!
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 我们复合好吗？</color>
[logic/ui/CViewCtrl.lua:104]:CLoginRewardNextView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667294
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:48:14
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [35.1,13.8,0] 30</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 不好我们早就分手了！</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 别忘记你喜欢的是谁！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 我知错了，我们复合好不好？</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 wuyu1 true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667304
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:48:24
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我已经不喜欢你了。</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [46.8,2.8,0] 116</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 weiqu true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  2 die</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  2 false</color>
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667314
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:48:34
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x6ad807c0"
|  |  AssociatedPick = "function: 0x6ad80820"
|  |  AssociatedSubmit = "function: 0x6ad807f0"
|  |  CreateDefalutData = "function: 0x6ad7e7a0"
|  |  GetChaptetFubenData = "function: 0x6ad804e0"
|  |  GetProgressThing = "function: 0x6ad80880"
|  |  GetRemainTime = "function: 0x6ad7e738"
|  |  GetStatus = "function: 0x6ad808e0"
|  |  GetTaskClientExtStrDic = "function: 0x6ad80850"
|  |  GetTaskTypeSpriteteName = "function: 0x6ad804b0"
|  |  GetTraceInfo = "function: 0x6ad80608"
|  |  GetTraceNpcType = "function: 0x6ad80480"
|  |  GetValue = "function: 0x6ad80548"
|  |  IsAbandon = "function: 0x6ad805a8"
|  |  IsAddEscortDynamicNpc = "function: 0x6ad80c18"
|  |  IsMissMengTask = "function: 0x6ad7e768"
|  |  IsPassChaterFuben = "function: 0x6ad80510"
|  |  IsTaskSpecityAction = "function: 0x6ad805d8"
|  |  IsTaskSpecityCategory = "function: 0x6ad80790"
|  |  New = "function: 0x6ad85110"
|  |  NewByData = "function: 0x6ad7dce0"
|  |  RaiseProgressIdx = "function: 0x6ad808b0"
|  |  RefreshTask = "function: 0x6ad80578"
|  |  ResetEndTime = "function: 0x6ad7e808"
|  |  SetStatus = "function: 0x6ad80c48"
|  |  classname = "CTask"
|  |  ctor = "function: 0x6ad7e708"
|  }
|  m_CData = {
|  |  ChapterFb = "1,6"
|  |  autoDoNextTask = 10020
|  |  clientExtStr = ""
|  |  name = "调用"
|  |  submitNpcId = 10310
|  |  submitRewardStr = {
|  |  |  [1] = "R1019"
|  |  }
|  |  taskWalkingTips = "这小孩真有趣;没想到看走眼遭报应了;none"
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,6"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10310
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 312
|  |  |  |  }
|  |  |  |  name = "伊露"
|  |  |  |  npcid = 27542
|  |  |  |  npctype = 10310
|  |  |  |  pos_info = {
|  |  |  |  |  x = 31000
|  |  |  |  |  y = 12000
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "执行官伊露命令你把逃跑的嘟噜鸟抓回来！"
|  |  isdone = 0
|  |  name = "调用"
|  |  patrolinfo = {}
|  |  pickiteminfo = {}
|  |  placeinfo = {
|  |  |  mapid = 101000
|  |  |  pos_x = 44
|  |  |  pos_y = 13
|  |  }
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10310
|  |  target = 10310
|  |  targetdesc = "伊露的命令"
|  |  taskid = 10019
|  |  taskitem = {}
|  |  tasktype = 3
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/global.lua:59]:<color=#ffeb04> Trigger............  27542</color>
[core/table.lua:94]:Table = {
|  map_id = 101000
|  model_info = {
|  |  scale = 1
|  |  shape = 312
|  }
|  name = "伊露"
|  npcid = 27542
|  npctype = 10310
|  pos_info = {
|  |  x = 31
|  |  y = 12
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8001
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickNpc = {
|  npcid = 27542
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8001
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcSay = {
|  name = "伊露"
|  npcid = 27542
|  shape = 312
|  text = "说多少次，别当我是小孩子！"
}
[core/global.lua:59]:<color=#ffeb04> fight  false</color>
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 7005
}
[core/table.lua:94]:<--Net Send: task.C2GSAcceptTask = {
|  taskid = 10019
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667324
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:48:44
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 7005
}
[core/table.lua:94]:-->Net Receive: task.GS2CDialog = {
|  dialog = {
|  |  [1] = {
|  |  |  content = "听清楚了！我是统帅部十二执行官，科学班负责人伊露。情况紧急，现在临时调用你，去抓捕逃跑的嘟噜鸟。"
|  |  |  next = "2"
|  |  |  pre_id_list = "10310,0"
|  |  |  status = 2
|  |  |  subid = 1
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [2] = {
|  |  |  content = "执行官？统帅部的执行官大人？"
|  |  |  next = "3"
|  |  |  pre_id_list = "10310,0"
|  |  |  status = 2
|  |  |  subid = 2
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [3] = {
|  |  |  content = "立刻，马上！"
|  |  |  finish_event = "_FP"
|  |  |  next = "0"
|  |  |  pre_id_list = "10310,0"
|  |  |  status = 2
|  |  |  subid = 3
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  }
|  dialog_id = 10019
|  npc_id = 27542
|  npc_name = "伊露"
|  sessionidx = "448041"
|  shape = 312
|  task_big_type = 2
|  task_small_type = 3
}
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshTask = {
|  accepttime = 1753667324
|  name = "调用"
|  statusinfo = {
|  |  note = "Accept Task"
|  |  status = 1
|  }
|  target = 10310
|  taskid = 10019
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667334
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:48:54
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667344
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:49:04
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667354
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:49:14
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667364
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:49:24
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 3</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 3003
}
[core/table.lua:94]:<--Net Send: other.C2GSCallback = {
|  sessionidx = "448041"
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 3003
}
[core/table.lua:94]:-->Net Receive: task.GS2CFindTaskPath = {
|  taskid = 10019
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10115</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [2] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [3] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [4] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10100</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小叶 1509 [40,14.6,0] 172 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [46.8,10.2,0] -160</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  诚哥 1150 [46.3,9,0] 7 false</color>
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667375
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:49:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 5
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 8
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 11
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我恨你！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不要恨我。</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我们分手吧</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 好。</color>
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667389
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:49:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [44,26,0] 60</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 其实我喜欢男生</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 shengqi true</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 3003
}
[core/table.lua:94]:<--Net Send: other.C2GSCallback = {
|  sessionidx = "449041"
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 3003
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowOpenBtn = {
|  sessionidx = "450041"
|  taskid = 10019
}
[logic/ui/CViewCtrl.lua:94]:CItemTipsProgressView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsProgressView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 3003
}
[core/table.lua:94]:<--Net Send: other.C2GSCallback = {
|  sessionidx = "450041"
}
[logic/ui/CViewCtrl.lua:104]:CItemTipsProgressView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 3003
}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshTask = {
|  accepttime = 1753667324
|  name = "调用"
|  statusinfo = {
|  |  note = "提交任务"
|  |  status = 5
|  }
|  target = 10310
|  taskid = 10019
}
[core/table.lua:94]:-->Net Receive: task.GS2CDelTask = {
|  done = 1
|  taskid = 10019
}
[core/table.lua:94]:-->Net Receive: task.GS2CAddTask = {
|  taskdata = {
|  |  acceptnpc = 5005
|  |  detaildesc = "问问路人有没有看到嘟噜鸟。"
|  |  name = "抓捕行动"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 5005
|  |  target = 5005
|  |  targetdesc = "询问路人"
|  |  taskid = 10020
|  |  taskitem = {}
|  |  tasktype = 1
|  |  traceinfo = {}
|  |  type = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>add task </color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x6ad807c0"
|  |  AssociatedPick = "function: 0x6ad80820"
|  |  AssociatedSubmit = "function: 0x6ad807f0"
|  |  CreateDefalutData = "function: 0x6ad7e7a0"
|  |  GetChaptetFubenData = "function: 0x6ad804e0"
|  |  GetProgressThing = "function: 0x6ad80880"
|  |  GetRemainTime = "function: 0x6ad7e738"
|  |  GetStatus = "function: 0x6ad808e0"
|  |  GetTaskClientExtStrDic = "function: 0x6ad80850"
|  |  GetTaskTypeSpriteteName = "function: 0x6ad804b0"
|  |  GetTraceInfo = "function: 0x6ad80608"
|  |  GetTraceNpcType = "function: 0x6ad80480"
|  |  GetValue = "function: 0x6ad80548"
|  |  IsAbandon = "function: 0x6ad805a8"
|  |  IsAddEscortDynamicNpc = "function: 0x6ad80c18"
|  |  IsMissMengTask = "function: 0x6ad7e768"
|  |  IsPassChaterFuben = "function: 0x6ad80510"
|  |  IsTaskSpecityAction = "function: 0x6ad805d8"
|  |  IsTaskSpecityCategory = "function: 0x6ad80790"
|  |  New = "function: 0x6ad85110"
|  |  NewByData = "function: 0x6ad7dce0"
|  |  RaiseProgressIdx = "function: 0x6ad808b0"
|  |  RefreshTask = "function: 0x6ad80578"
|  |  ResetEndTime = "function: 0x6ad7e808"
|  |  SetStatus = "function: 0x6ad80c48"
|  |  classname = "CTask"
|  |  ctor = "function: 0x6ad7e708"
|  }
|  m_CData = {
|  |  ChapterFb = "1,6"
|  |  autoDoNextTask = 10379
|  |  clientExtStr = ""
|  |  name = "抓捕行动"
|  |  submitNpcId = 5005
|  |  submitRewardStr = {
|  |  |  [1] = "R1020"
|  |  }
|  |  taskWalkingTips = "跑哪了？;none;终于让我找到你了！"
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5005
|  |  autotype = 1
|  |  detaildesc = "问问路人有没有看到嘟噜鸟。"
|  |  isdone = 0
|  |  name = "抓捕行动"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 5005
|  |  target = 5005
|  |  targetdesc = "询问路人"
|  |  taskid = 10020
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  abnormal_attr_ratio = 550
|  |  attack = 315
|  |  coin = 61800
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  energy = 123
|  |  exp = 15110
|  |  grade = 11
|  |  hp = 2846
|  |  kp_sdk_info = {
|  |  |  create_time = **********
|  |  |  upgrade_time = 1753667397
|  |  }
|  |  mask = "10020000287ff62"
|  |  max_hp = 2928
|  |  power = 921
|  |  res_abnormal_ratio = 550
|  |  res_critical_ratio = 500
|  |  skill_point = 10
|  |  speed = 753
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 550
|  attack = 315
|  coin = 61800
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  energy = 123
|  exp = 15110
|  grade = 11
|  hp = 2846
|  kp_sdk_info = {
|  |  create_time = **********
|  |  upgrade_time = 1753667397
|  }
|  max_hp = 2928
|  power = 921
|  res_abnormal_ratio = 550
|  res_critical_ratio = 500
|  skill_point = 10
|  speed = 753
}
[logic/platform/CSdkCtrl.lua:393]:sdk upload: not init
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Achieve"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/attr/CAttrCtrl"]:143: in function 'UpdateAttr'
	[string "net/netplayer"]:9: in function <[string "net/netplayer"]:5>
	[C]: in function 'xxpcall'
	[string "net/CNetCtrl"]:318: in function 'ProtoCall'
	[string "net/CNetCtrl"]:300: in function 'ProcessProto'
	[string "net/CNetCtrl"]:251: in function 'ProtoHandlerCheck'
	[string "net/CNetCtrl"]:262: in function 'Receive'
	[string "net/CNetCtrl"]:175: in function 'OnSocketEvent'
	[string "net/CNetCtrl"]:111: in function 'm_Callback'
	[string "net/CTcpClient"]:43: in function <[string "net/CTcpClient"]:37>
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 10170
|  |  mask = "80"
|  }
|  partnerid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 10170
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 5350
|  |  mask = "80"
|  }
|  partnerid = 2
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 5350
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 2770
|  |  mask = "80"
|  }
|  partnerid = 3
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 2770
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得#exp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#exp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#w1#G2450#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G2450#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B重华#n获得#hexp#G300#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B重华#n获得#hexp#G300#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B马面面#n获得#hexp#G300#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B马面面#n获得#hexp#G300#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B蛇姬#n获得#hexp#G300#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B蛇姬#n获得#hexp#G300#n"
|  type = 6
}
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 Open_Achieve</color>
[logic/ui/CViewCtrl.lua:94]:CGuideView ShowView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Achieve"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/task/CTaskCtrl"]:2434: in function 'StartAutoDoingShiMen'
	[string "logic/guide/CGuideCtrl"]:2383: in function 'StopActionWhenGuide'
	[string "logic/guide/CGuideView"]:31: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:311: in function <[string "logic/base/CResCtrl"]:305>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[logic/ui/CViewBase.lua:125]:CGuideView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 Open_Achieve_1 1</color>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Achieve"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Achieve"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Achieve"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/mainmenu/CMainMenuExpandBox"]:280: in function <[string "logic/mainmenu/CMainMenuExpandBox"]:251>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
[core/table.lua:94]:Table = {
|  [1] = "16"
|  [2] = "20"
}
[core/global.lua:59]:<color=#ffeb04>11 16 20</color>
[core/table.lua:94]:Table = {
|  [1] = "21"
|  [2] = "24"
}
[core/global.lua:59]:<color=#ffeb04>11 21 24</color>
[core/table.lua:94]:Table = {
|  [1] = "25"
|  [2] = "27"
}
[core/global.lua:59]:<color=#ffeb04>11 25 27</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  2 false</color>
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667399
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:49:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>引导结束 Open_Achieve Open_Achieve_1 1</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Achieve_1"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1001001
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideView     CloseView
[logic/guide/CGuideCtrl.lua:333]:已完成引导Open_Achieve
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Achieve"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1010099
|  }
}
[core/global.lua:59]:<color=#ffeb04>停止引导检查,并且下一帧再检测引导</color>
[logic/ui/CViewBase.lua:125]:CGuideMaskView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667409
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:50:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CWelfareView ShowView
[logic/ui/CViewBase.lua:125]:CWelfareView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CItemPartnertSelectPackageView ShowView
[logic/ui/CViewBase.lua:125]:CItemPartnertSelectPackageView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667419
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:50:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CItemPartnertSelectPackageView     CloseView
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10107</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [2] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [3] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [4] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
}
[logic/ui/CViewCtrl.lua:104]:CWelfareView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667429
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:50:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667439
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:50:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667449
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:50:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x6ad807c0"
|  |  AssociatedPick = "function: 0x6ad80820"
|  |  AssociatedSubmit = "function: 0x6ad807f0"
|  |  CreateDefalutData = "function: 0x6ad7e7a0"
|  |  GetChaptetFubenData = "function: 0x6ad804e0"
|  |  GetProgressThing = "function: 0x6ad80880"
|  |  GetRemainTime = "function: 0x6ad7e738"
|  |  GetStatus = "function: 0x6ad808e0"
|  |  GetTaskClientExtStrDic = "function: 0x6ad80850"
|  |  GetTaskTypeSpriteteName = "function: 0x6ad804b0"
|  |  GetTraceInfo = "function: 0x6ad80608"
|  |  GetTraceNpcType = "function: 0x6ad80480"
|  |  GetValue = "function: 0x6ad80548"
|  |  IsAbandon = "function: 0x6ad805a8"
|  |  IsAddEscortDynamicNpc = "function: 0x6ad80c18"
|  |  IsMissMengTask = "function: 0x6ad7e768"
|  |  IsPassChaterFuben = "function: 0x6ad80510"
|  |  IsTaskSpecityAction = "function: 0x6ad805d8"
|  |  IsTaskSpecityCategory = "function: 0x6ad80790"
|  |  New = "function: 0x6ad85110"
|  |  NewByData = "function: 0x6ad7dce0"
|  |  RaiseProgressIdx = "function: 0x6ad808b0"
|  |  RefreshTask = "function: 0x6ad80578"
|  |  ResetEndTime = "function: 0x6ad7e808"
|  |  SetStatus = "function: 0x6ad80c48"
|  |  classname = "CTask"
|  |  ctor = "function: 0x6ad7e708"
|  }
|  m_CData = {
|  |  ChapterFb = "1,6"
|  |  autoDoNextTask = 10379
|  |  clientExtStr = ""
|  |  name = "抓捕行动"
|  |  submitNpcId = 5005
|  |  submitRewardStr = {
|  |  |  [1] = "R1020"
|  |  }
|  |  taskWalkingTips = "跑哪了？;none;终于让我找到你了！"
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,6"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5005
|  |  autotype = 1
|  |  detaildesc = "问问路人有没有看到嘟噜鸟。"
|  |  isdone = 0
|  |  name = "抓捕行动"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 5005
|  |  target = 5005
|  |  targetdesc = "询问路人"
|  |  taskid = 10020
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8001
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickNpc = {
|  npcid = 72
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8001
}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcSay = {
|  fight = true
|  name = "遥"
|  npcid = 72
|  shape = 1011
|  text = "你也来祷告吗？"
}
[core/global.lua:59]:<color=#ffeb04> fight  true</color>
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 7005
}
[core/table.lua:94]:<--Net Send: task.C2GSAcceptTask = {
|  taskid = 10020
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 4
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1010
|  |  |  }
|  |  |  name = "丽丝"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 69
|  |  npctype = 5004
|  }
|  eid = 12
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 12
|  pos_info = {
|  |  x = 45600
|  |  y = 26900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1010
|  }
|  name = "丽丝"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 18
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 7005
}
[core/table.lua:94]:-->Net Receive: task.GS2CDialog = {
|  dialog = {
|  |  [1] = {
|  |  |  content = "请问你看到有一只跑得飞快的鸟路过吗？"
|  |  |  next = "2"
|  |  |  pre_id_list = "5005,0"
|  |  |  status = 2
|  |  |  subid = 1
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [2] = {
|  |  |  content = "鸟不应该在天上飞怎么会跑呢。不过倒是有一只鸡刚刚往教会方向跑去了。"
|  |  |  next = "3"
|  |  |  pre_id_list = "5005,0"
|  |  |  status = 2
|  |  |  subid = 2
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [3] = {
|  |  |  content = "就是它了，谢谢你。"
|  |  |  finish_event = "DONE"
|  |  |  next = "0"
|  |  |  pre_id_list = "5005,0"
|  |  |  status = 2
|  |  |  subid = 3
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  }
|  dialog_id = 10020
|  npc_id = 73
|  npc_name = "遥"
|  sessionidx = "451041"
|  shape = 1011
|  task_big_type = 2
|  task_small_type = 1
}
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshTask = {
|  accepttime = 1753667453
|  name = "抓捕行动"
|  statusinfo = {
|  |  note = "Accept Task"
|  |  status = 1
|  }
|  target = 5005
|  taskid = 10020
}
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/global.lua:59]:<color=#ffeb04>OnNext 1</color>
[core/global.lua:59]:<color=#ffeb04>OnNext 1</color>
[core/global.lua:59]:<color=#ffeb04>OnNext 3</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 3003
}
[core/table.lua:94]:<--Net Send: other.C2GSCallback = {
|  sessionidx = "451041"
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 3003
}
[core/table.lua:94]:-->Net Receive: task.GS2CDelTask = {
|  done = 1
|  taskid = 10020
}
[core/table.lua:94]:-->Net Receive: task.GS2CAddTask = {
|  taskdata = {
|  |  acceptnpc = 11432
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 418
|  |  |  |  }
|  |  |  |  name = "嘟嘟噜"
|  |  |  |  npcid = 27543
|  |  |  |  npctype = 11432
|  |  |  |  pos_info = {
|  |  |  |  |  x = 47500
|  |  |  |  |  y = 23000
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "作为一名即将参加武道大会的选手，怎么可能连一只嘟噜鸟都抓不住。"
|  |  name = "继续抓捕"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 5004
|  |  target = 11432
|  |  targetdesc = "跟上嘟嘟噜"
|  |  taskid = 10379
|  |  taskitem = {}
|  |  tasktype = 9
|  |  traceinfo = {
|  |  |  mapid = 101000
|  |  |  npctype = 11432
|  |  |  pos_x = 44700
|  |  |  pos_y = 26500
|  |  }
|  |  type = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>add task </color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x6ad807c0"
|  |  AssociatedPick = "function: 0x6ad80820"
|  |  AssociatedSubmit = "function: 0x6ad807f0"
|  |  CreateDefalutData = "function: 0x6ad7e7a0"
|  |  GetChaptetFubenData = "function: 0x6ad804e0"
|  |  GetProgressThing = "function: 0x6ad80880"
|  |  GetRemainTime = "function: 0x6ad7e738"
|  |  GetStatus = "function: 0x6ad808e0"
|  |  GetTaskClientExtStrDic = "function: 0x6ad80850"
|  |  GetTaskTypeSpriteteName = "function: 0x6ad804b0"
|  |  GetTraceInfo = "function: 0x6ad80608"
|  |  GetTraceNpcType = "function: 0x6ad80480"
|  |  GetValue = "function: 0x6ad80548"
|  |  IsAbandon = "function: 0x6ad805a8"
|  |  IsAddEscortDynamicNpc = "function: 0x6ad80c18"
|  |  IsMissMengTask = "function: 0x6ad7e768"
|  |  IsPassChaterFuben = "function: 0x6ad80510"
|  |  IsTaskSpecityAction = "function: 0x6ad805d8"
|  |  IsTaskSpecityCategory = "function: 0x6ad80790"
|  |  New = "function: 0x6ad85110"
|  |  NewByData = "function: 0x6ad7dce0"
|  |  RaiseProgressIdx = "function: 0x6ad808b0"
|  |  RefreshTask = "function: 0x6ad80578"
|  |  ResetEndTime = "function: 0x6ad7e808"
|  |  SetStatus = "function: 0x6ad80c48"
|  |  classname = "CTask"
|  |  ctor = "function: 0x6ad7e708"
|  }
|  m_CData = {
|  |  ChapterFb = "1,6"
|  |  autoDoNextTask = 10380
|  |  clientExtStr = ""
|  |  name = "继续抓捕"
|  |  submitNpcId = 5004
|  |  submitRewardStr = {
|  |  |  [1] = "R1379"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 11432
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 418
|  |  |  |  }
|  |  |  |  name = "嘟嘟噜"
|  |  |  |  npcid = 27543
|  |  |  |  npctype = 11432
|  |  |  |  pos_info = {
|  |  |  |  |  x = 47500
|  |  |  |  |  y = 23000
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "作为一名即将参加武道大会的选手，怎么可能连一只嘟噜鸟都抓不住。"
|  |  isdone = 0
|  |  name = "继续抓捕"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 5004
|  |  target = 11432
|  |  targetdesc = "跟上嘟嘟噜"
|  |  taskid = 10379
|  |  taskitem = {}
|  |  tasktype = 9
|  |  time = 0
|  |  traceinfo = {
|  |  |  mapid = 101000
|  |  |  npctype = 11432
|  |  |  pos_x = 44700
|  |  |  pos_y = 26500
|  |  }
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChapterOpen = {
|  chapter = 1
|  level = 7
|  type = 1
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  coin = 64280
|  |  exp = 15660
|  |  mask = "60"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  coin = 64280
|  exp = 15660
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 10470
|  |  mask = "80"
|  }
|  partnerid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 10470
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  abnormal_attr_ratio = 400
|  |  attack = 349
|  |  critical_damage = 15000
|  |  critical_ratio = 800
|  |  cure_critical_ratio = 500
|  |  defense = 79
|  |  exp = 5650
|  |  grade = 7
|  |  hp = 2611
|  |  mask = "fffc0"
|  |  max_hp = 2611
|  |  power = 882
|  |  res_abnormal_ratio = 500
|  |  res_critical_ratio = 500
|  |  speed = 315
|  }
|  partnerid = 2
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  abnormal_attr_ratio = 400
|  attack = 349
|  critical_damage = 15000
|  critical_ratio = 800
|  cure_critical_ratio = 500
|  defense = 79
|  exp = 5650
|  grade = 7
|  hp = 2611
|  max_hp = 2611
|  power = 882
|  res_abnormal_ratio = 500
|  res_critical_ratio = 500
|  speed = 315
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 3070
|  |  mask = "80"
|  }
|  partnerid = 3
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 3070
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得#exp#G550#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#exp#G550#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#w1#G2480#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G2480#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 502
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 502
|  |  |  |  value = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 502
|  |  |  |  value = 7
|  |  |  }
|  |  }
|  |  id = 1
|  }
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B重华#n获得#hexp#G300#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B重华#n获得#hexp#G300#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B马面面#n获得#hexp#G300#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B马面面#n获得#hexp#G300#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B蛇姬#n获得#hexp#G300#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B蛇姬#n获得#hexp#G300#n"
|  type = 6
}
[core/table.lua:94]:Table = {
|  mapid = 101000
|  npctype = 11432
|  pos_x = 44700
|  pos_y = 26500
}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x6ad807c0"
|  |  AssociatedPick = "function: 0x6ad80820"
|  |  AssociatedSubmit = "function: 0x6ad807f0"
|  |  CreateDefalutData = "function: 0x6ad7e7a0"
|  |  GetChaptetFubenData = "function: 0x6ad804e0"
|  |  GetProgressThing = "function: 0x6ad80880"
|  |  GetRemainTime = "function: 0x6ad7e738"
|  |  GetStatus = "function: 0x6ad808e0"
|  |  GetTaskClientExtStrDic = "function: 0x6ad80850"
|  |  GetTaskTypeSpriteteName = "function: 0x6ad804b0"
|  |  GetTraceInfo = "function: 0x6ad80608"
|  |  GetTraceNpcType = "function: 0x6ad80480"
|  |  GetValue = "function: 0x6ad80548"
|  |  IsAbandon = "function: 0x6ad805a8"
|  |  IsAddEscortDynamicNpc = "function: 0x6ad80c18"
|  |  IsMissMengTask = "function: 0x6ad7e768"
|  |  IsPassChaterFuben = "function: 0x6ad80510"
|  |  IsTaskSpecityAction = "function: 0x6ad805d8"
|  |  IsTaskSpecityCategory = "function: 0x6ad80790"
|  |  New = "function: 0x6ad85110"
|  |  NewByData = "function: 0x6ad7dce0"
|  |  RaiseProgressIdx = "function: 0x6ad808b0"
|  |  RefreshTask = "function: 0x6ad80578"
|  |  ResetEndTime = "function: 0x6ad7e808"
|  |  SetStatus = "function: 0x6ad80c48"
|  |  classname = "CTask"
|  |  ctor = "function: 0x6ad7e708"
|  }
|  m_CData = {
|  |  ChapterFb = "1,6"
|  |  autoDoNextTask = 10380
|  |  clientExtStr = ""
|  |  name = "继续抓捕"
|  |  submitNpcId = 5004
|  |  submitRewardStr = {
|  |  |  [1] = "R1379"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,6"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 11432
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 418
|  |  |  |  }
|  |  |  |  name = "嘟嘟噜"
|  |  |  |  npcid = 27543
|  |  |  |  npctype = 11432
|  |  |  |  pos_info = {
|  |  |  |  |  x = 47500
|  |  |  |  |  y = 23000
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "作为一名即将参加武道大会的选手，怎么可能连一只嘟噜鸟都抓不住。"
|  |  isdone = 0
|  |  name = "继续抓捕"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 5004
|  |  target = 11432
|  |  targetdesc = "跟上嘟嘟噜"
|  |  taskid = 10379
|  |  taskitem = {}
|  |  tasktype = 9
|  |  time = 0
|  |  traceinfo = {
|  |  |  mapid = 101000
|  |  |  npctype = 11432
|  |  |  pos_x = 44700
|  |  |  pos_y = 26500
|  |  }
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
|  m_TraceInfo = {
|  |  mapid = 101000
|  |  npctype = 11432
|  |  pos_x = 44700
|  |  pos_y = 26500
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667459
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:50:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 6
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 19
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10100</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [2] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [3] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [4] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [5] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667469
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:51:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667479
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:51:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x6ad807c0"
|  |  AssociatedPick = "function: 0x6ad80820"
|  |  AssociatedSubmit = "function: 0x6ad807f0"
|  |  CreateDefalutData = "function: 0x6ad7e7a0"
|  |  GetChaptetFubenData = "function: 0x6ad804e0"
|  |  GetProgressThing = "function: 0x6ad80880"
|  |  GetRemainTime = "function: 0x6ad7e738"
|  |  GetStatus = "function: 0x6ad808e0"
|  |  GetTaskClientExtStrDic = "function: 0x6ad80850"
|  |  GetTaskTypeSpriteteName = "function: 0x6ad804b0"
|  |  GetTraceInfo = "function: 0x6ad80608"
|  |  GetTraceNpcType = "function: 0x6ad80480"
|  |  GetValue = "function: 0x6ad80548"
|  |  IsAbandon = "function: 0x6ad805a8"
|  |  IsAddEscortDynamicNpc = "function: 0x6ad80c18"
|  |  IsMissMengTask = "function: 0x6ad7e768"
|  |  IsPassChaterFuben = "function: 0x6ad80510"
|  |  IsTaskSpecityAction = "function: 0x6ad805d8"
|  |  IsTaskSpecityCategory = "function: 0x6ad80790"
|  |  New = "function: 0x6ad85110"
|  |  NewByData = "function: 0x6ad7dce0"
|  |  RaiseProgressIdx = "function: 0x6ad808b0"
|  |  RefreshTask = "function: 0x6ad80578"
|  |  ResetEndTime = "function: 0x6ad7e808"
|  |  SetStatus = "function: 0x6ad80c48"
|  |  classname = "CTask"
|  |  ctor = "function: 0x6ad7e708"
|  }
|  m_CData = {
|  |  ChapterFb = "1,6"
|  |  autoDoNextTask = 10380
|  |  clientExtStr = ""
|  |  name = "继续抓捕"
|  |  submitNpcId = 5004
|  |  submitRewardStr = {
|  |  |  [1] = "R1379"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,6"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 11432
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 418
|  |  |  |  }
|  |  |  |  name = "嘟嘟噜"
|  |  |  |  npcid = 27543
|  |  |  |  npctype = 11432
|  |  |  |  pos_info = {
|  |  |  |  |  x = 47500
|  |  |  |  |  y = 23000
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "作为一名即将参加武道大会的选手，怎么可能连一只嘟噜鸟都抓不住。"
|  |  isdone = 0
|  |  name = "继续抓捕"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 5004
|  |  target = 11432
|  |  targetdesc = "跟上嘟嘟噜"
|  |  taskid = 10379
|  |  taskitem = {}
|  |  tasktype = 9
|  |  time = 0
|  |  traceinfo = {
|  |  |  mapid = 101000
|  |  |  npctype = 11432
|  |  |  pos_x = 44700
|  |  |  pos_y = 26500
|  |  }
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
|  m_TraceInfo = {
|  |  mapid = 101000
|  |  npctype = 11432
|  |  pos_x = 44700
|  |  pos_y = 26500
|  }
}
[core/global.lua:59]:<color=#ffeb04> Trigger............  27543</color>
[core/table.lua:94]:Table = {
|  map_id = 101000
|  model_info = {
|  |  scale = 1
|  |  shape = 418
|  }
|  name = "嘟嘟噜"
|  npcid = 27543
|  npctype = 11432
|  pos_info = {
|  |  x = 47.5
|  |  y = 23
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8001
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickNpc = {
|  npcid = 27543
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8001
}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcSay = {
|  name = "嘟嘟噜"
|  npcid = 27543
|  shape = 418
|  text = "嘟嘟噜嘟嘟噜~"
}
[core/global.lua:59]:<color=#ffeb04> fight  false</color>
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1753667489
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/07/28 09:51:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 7005
}
[core/table.lua:94]:<--Net Send: task.C2GSAcceptTask = {
|  taskid = 10379
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 7005
}
[core/table.lua:94]:-->Net Receive: task.GS2CDialog = {
|  dialog = {
|  |  [1] = {
|  |  |  content = "嘟噜！"
|  |  |  next = "2"
|  |  |  pre_id_list = "11432,0"
|  |  |  status = 2
|  |  |  subid = 1
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [2] = {
|  |  |  content = "莫非你故意留在这里等我？"
|  |  |  next = "3"
|  |  |  pre_id_list = "11432,0"
|  |  |  status = 2
|  |  |  subid = 2
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [3] = {
|  |  |  content = "嘟噜嘟噜！"
|  |  |  next = "4"
|  |  |  pre_id_list = "11432,0"
|  |  |  status = 2
|  |  |  subid = 3
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [4] = {
|  |  |  content = "什么意思，你要跟我回去吗？"
|  |  |  next = "5"
|  |  |  pre_id_list = "11432,0"
|  |  |  status = 2
|  |  |  subid = 4
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [5] = {
|  |  |  content = "嘟噜！"
|  |  |  next = "6"
|  |  |  pre_id_list = "11432,0"
|  |  |  status = 2
|  |  |  subid = 5
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [6] = {
|  |  |  content = "呸呸呸！敢喷我一脸的沙子，可恶你休想逃！"
|  |  |  finish_event = "_STARTESCORT"
|  |  |  next = "0"
|  |  |  pre_id_list = "11432,0"
|  |  |  status = 2
|  |  |  subid = 6
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  }
|  dialog_id = 10379
|  npc_id = 27543
|  npc_name = "嘟嘟噜"
|  sessionidx = "452041"
|  shape = 418
|  task_big_type = 2
|  task_small_type = 9
}
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshTask = {
|  accepttime = 1753667490
|  name = "继续抓捕"
|  statusinfo = {
|  |  note = "Accept Task"
|  |  status = 1
|  }
|  target = 5004
|  taskid = 10379
}
[core/table.lua:94]:Table = {
|  mapid = 101000
|  npctype = 11432
|  pos_x = 44700
|  pos_y = 26500
}
[core/table.lua:94]:<--Net Send: task.C2GSSyncTraceInfo = {
|  cur_mapid = 101000
|  cur_posx = 47500
|  cur_posy = 23000
|  taskid = 10379
}
[core/global.lua:59]:<color=#ffeb04>OnNext 1</color>
[core/global.lua:59]:<color=#ffeb04>OnNext 1</color>
[core/global.lua:59]:<color=#ffeb04>OnNext 1</color>
[core/global.lua:59]:<color=#ffeb04>OnNext 1</color>
[core/global.lua:59]:<color=#ffeb04>OnNext 1</color>
[core/global.lua:59]:<color=#ffeb04>OnNext 3</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 3003
}
[core/table.lua:94]:<--Net Send: other.C2GSCallback = {
|  sessionidx = "452041"
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 3003
}
[core/table.lua:94]:-->Net Receive: task.GS2CStartEscort = {
|  taskid = 10379
}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x6ad807c0"
|  |  AssociatedPick = "function: 0x6ad80820"
|  |  AssociatedSubmit = "function: 0x6ad807f0"
|  |  CreateDefalutData = "function: 0x6ad7e7a0"
|  |  GetChaptetFubenData = "function: 0x6ad804e0"
|  |  GetProgressThing = "function: 0x6ad80880"
|  |  GetRemainTime = "function: 0x6ad7e738"
|  |  GetStatus = "function: 0x6ad808e0"
|  |  GetTaskClientExtStrDic = "function: 0x6ad80850"
|  |  GetTaskTypeSpriteteName = "function: 0x6ad804b0"
|  |  GetTraceInfo = "function: 0x6ad80608"
|  |  GetTraceNpcType = "function: 0x6ad80480"
|  |  GetValue = "function: 0x6ad80548"
|  |  IsAbandon = "function: 0x6ad805a8"
|  |  IsAddEscortDynamicNpc = "function: 0x6ad80c18"
|  |  IsMissMengTask = "function: 0x6ad7e768"
|  |  IsPassChaterFuben = "function: 0x6ad80510"
|  |  IsTaskSpecityAction = "function: 0x6ad805d8"
|  |  IsTaskSpecityCategory = "function: 0x6ad80790"
|  |  New = "function: 0x6ad85110"
|  |  NewByData = "function: 0x6ad7dce0"
|  |  RaiseProgressIdx = "function: 0x6ad808b0"
|  |  RefreshTask = "function: 0x6ad80578"
|  |  ResetEndTime = "function: 0x6ad7e808"
|  |  SetStatus = "function: 0x6ad80c48"
|  |  classname = "CTask"
|  |  ctor = "function: 0x6ad7e708"
|  }
|  m_CData = {
|  |  ChapterFb = "1,6"
|  |  autoDoNextTask = 10380
|  |  clientExtStr = ""
|  |  name = "继续抓捕"
|  |  submitNpcId = 5004
|  |  submitRewardStr = {
|  |  |  [1] = "R1379"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,6"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 11432
|  |  accepttime = 1753667490
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 418
|  |  |  |  }
|  |  |  |  name = "嘟嘟噜"
|  |  |  |  npcid = 27543
|  |  |  |  npctype = 11432
|  |  |  |  pos_info = {
|  |  |  |  |  x = 47500
|  |  |  |  |  y = 23000
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "作为一名即将参加武道大会的选手，怎么可能连一只嘟噜鸟都抓不住。"
|  |  isdone = 0
|  |  name = "继续抓捕"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "Accept Task"
|  |  |  status = 1
|  |  }
|  |  submitnpc = 5004
|  |  target = 5004
|  |  targetdesc = "跟上嘟嘟噜"
|  |  taskid = 10379
|  |  taskitem = {}
|  |  tasktype = 9
|  |  time = 0
|  |  traceinfo = {
|  |  |  mapid = 101000
|  |  |  npctype = 11432
|  |  |  pos_x = 44700
|  |  |  pos_y = 26500
|  |  }
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
|  m_TraceInfo = {
|  |  cur_mapid = 101000
|  |  cur_posx = 47.5
|  |  cur_posy = 23
|  |  mapid = 101000
|  |  npctype = 11432
|  |  pos_x = 44700
|  |  pos_y = 26500
|  }
}
[core/global.lua:59]:<color=#ffeb04> 开始移动 44.7 26.5</color>
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: task.C2GSSyncTraceInfo = {
|  cur_mapid = 101000
|  cur_posx = 47200
|  cur_posy = 23300
|  taskid = 10379
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 2
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 14
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 15
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 16
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
